# BNO080 I2C传感器驱动项目

## 项目概述

本项目是基于STM32F103C8T6微控制器的BNO080惯性测量传感器驱动程序，使用I2C通信协议。BNO080是Bosch公司生产的智能9轴绝对方向传感器，内置传感器融合算法，可以直接输出四元数和欧拉角数据。

## 主要特性

- **I2C通信**：使用400kHz快速I2C通信，节省引脚资源
- **实时数据**：支持100Hz数据更新频率
- **传感器融合**：利用BNO080内置算法，直接获取姿态数据
- **错误处理**：完善的错误检测、统计和自动恢复机制
- **低功耗**：支持低功耗模式，适合电池供电应用
- **调试功能**：丰富的调试和诊断功能
- **单元测试**：完整的单元测试和集成测试框架

## 硬件配置

### 微控制器
- **型号**：STM32F103C8T6
- **时钟**：72MHz系统时钟
- **封装**：LQFP48

### BNO080传感器
- **通信接口**：I2C (400kHz)
- **I2C地址**：0x4A (ADR引脚接GND) 或 0x4B (ADR引脚接VCC)
- **协议模式**：PS0=0, PS1=0 (I2C模式)

### 引脚连接

| STM32引脚 | BNO080引脚 | 功能 | 说明 |
|-----------|------------|------|------|
| PB6 | SCL | I2C时钟 | 需要上拉电阻 |
| PB7 | SDA | I2C数据 | 需要上拉电阻 |
| PB0 | PS0 | 协议选择0 | 设置为低电平 |
| PB1 | PS1 | 协议选择1 | 设置为低电平 |
| 3.3V | VCC | 电源 | 3.3V供电 |
| GND | GND | 地线 | 公共地 |

### 调试接口
- **UART2**：PA2(TX), PA3(RX) - 115200波特率调试输出

## 软件架构

```
应用层 (main.c)
    ↓
BNO080 HAL层 (bno080_hal.h/c)
    ↓
I2C通信层 (i2c.h/c)
    ↓
STM32 HAL驱动
    ↓
硬件I2C接口
```

## 主要文件说明

### 核心文件
- `bno080_hal.h/c` - BNO080传感器HAL层驱动
- `i2c.h/c` - I2C通信接口
- `tim.h/c` - 定时器配置（100Hz数据读取）
- `main.c` - 主程序

### 测试文件
- `bno080_test.h/c` - 单元测试框架
- `bno080_integration_test.h/c` - 集成测试框架

### 配置文件
- `BNO080.ioc` - STM32CubeMX配置文件

## 快速开始

### 1. 硬件连接
按照上述引脚连接表连接STM32和BNO080传感器。

### 2. 编译和下载
1. 使用Keil MDK-ARM或STM32CubeIDE打开项目
2. 编译项目
3. 下载到STM32开发板

### 3. 运行程序
1. 连接串口调试工具到UART2 (115200波特率)
2. 复位开发板
3. 观察串口输出的姿态数据

### 4. 数据格式
程序会以CSV格式输出欧拉角数据：
```
偏航角,俯仰角,横滚角
45.23,-12.45,3.67
```

## API接口

### 初始化函数
```c
void BNO080_Init(void);
```
初始化BNO080传感器，配置I2C通信和传感器参数。

### 数据获取函数
```c
void BNO080_GetData(BNO080_Data_t* data_ptr);
```
获取最新的传感器数据。

### 数据结构
```c
typedef struct {
    float yaw;      // 偏航角 (度)
    float pitch;    // 俯仰角 (度)
    float roll;     // 横滚角 (度)
    volatile uint8_t new_data_flag;  // 新数据标志
    uint32_t last_update_time;  // 最后更新时间
    uint32_t error_count;       // 错误计数
} BNO080_Data_t;
```

## 高级功能

### 错误处理
```c
// 获取错误统计
BNO080_ErrorStats_t stats;
BNO080_GetErrorStats(&stats);

// 检查设备状态
uint8_t online = BNO080_IsDeviceOnline();
```

### 调试功能
```c
// 设置调试级别
BNO080_SetDebugLevel(BNO080_DEBUG_DETAILED);

// 打印诊断信息
BNO080_PrintDiagnostics();

// 运行测试模式
BNO080_TestMode();
```

### 低功耗模式
```c
// 进入低功耗模式
BNO080_EnterLowPowerMode();

// 退出低功耗模式
BNO080_ExitLowPowerMode();
```

## 测试功能

### 单元测试
在main.c中取消注释以下行来运行单元测试：
```c
BNO080_RunAllTests();
```

### 集成测试
在main.c中取消注释以下行来运行集成测试：
```c
BNO080_RunIntegrationTests();
```

## 故障排除

### 常见问题

#### 1. 设备检测失败
**现象**：串口输出"BNO080 I2C设备未找到"
**解决方案**：
- 检查I2C引脚连接
- 确认上拉电阻已连接
- 检查BNO080供电是否正常
- 验证PS0和PS1引脚配置

#### 2. 数据不更新
**现象**：new_data_flag始终为0
**解决方案**：
- 检查定时器是否正常启动
- 验证传感器配置是否成功
- 检查I2C通信是否正常

#### 3. 数据异常
**现象**：角度数据为NaN或超出范围
**解决方案**：
- 运行测试模式检查硬件
- 检查传感器校准状态
- 验证四元数转换算法

#### 4. 通信错误
**现象**：频繁的I2C超时或NACK错误
**解决方案**：
- 检查I2C时钟频率设置
- 验证上拉电阻值（推荐4.7kΩ）
- 检查线路长度和干扰

### 调试步骤

1. **基础检查**
   ```c
   BNO080_TestMode();  // 运行测试模式
   ```

2. **详细诊断**
   ```c
   BNO080_PrintDiagnostics();  // 打印诊断信息
   ```

3. **错误统计**
   ```c
   BNO080_ErrorStats_t stats;
   BNO080_GetErrorStats(&stats);
   // 检查各类错误计数
   ```

## 性能指标

- **数据更新频率**：100Hz
- **I2C通信速度**：400kHz
- **角度精度**：0.01度
- **CPU占用率**：<5%
- **内存占用**：约1KB RAM

## 开发环境

- **IDE**：Keil MDK-ARM V5.32 或 STM32CubeIDE
- **HAL库**：STM32Cube FW_F1 V1.8.6
- **编译器**：ARM Compiler V6

## 版本历史

### V1.0.0 (2025-01-XX)
- 初始版本
- 支持I2C通信
- 实现基础姿态数据读取
- 添加错误处理和调试功能
- 完整的测试框架

## 许可证

本项目基于MIT许可证开源。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目仓库：[GitHub链接]
- 邮箱：[联系邮箱]

## 致谢

感谢Bosch公司提供的BNO080传感器和相关技术文档。