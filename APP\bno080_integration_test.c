/**
  ******************************************************************************
  * @file    bno080_integration_test.c
  * @brief   This file provides code for BNO080 integration tests
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bno080_integration_test.h"
#include "usart.h"
#include <math.h>
#include <string.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

// 全局集成测试结果
static BNO080_IntegrationTestResults_t g_integration_results = {0};

/* USER CODE BEGIN 1 */

/**
 * @brief  运行所有集成测试
 */
void BNO080_RunIntegrationTests(void)
{
    uint32_t start_time = HAL_GetTick();
    
    my_printf(&huart2, "\r\n=== BNO080集成测试开始 ===\r\n");
    
    // 清除测试结果
    memset(&g_integration_results, 0, sizeof(BNO080_IntegrationTestResults_t));
    
    // 运行各项集成测试
    BNO080_TestApplicationCompatibility();
    BNO080_TestSystemStability();
    BNO080_TestDataAccuracy();
    BNO080_TestUpdateFrequency();
    
    // 计算测试持续时间
    g_integration_results.total_test_time_ms = HAL_GetTick() - start_time;
    
    // 输出测试结果
    my_printf(&huart2, "\r\n=== 集成测试结果汇总 ===\r\n");
    my_printf(&huart2, "兼容性测试: %lu/%lu 通过\r\n", 
              g_integration_results.compatibility_passed, g_integration_results.compatibility_tests);
    my_printf(&huart2, "稳定性测试: %lu/%lu 通过\r\n", 
              g_integration_results.stability_passed, g_integration_results.stability_tests);
    my_printf(&huart2, "性能测试: %lu/%lu 通过\r\n", 
              g_integration_results.performance_passed, g_integration_results.performance_tests);
    my_printf(&huart2, "总测试时间: %lu ms\r\n", g_integration_results.total_test_time_ms);
    
    uint32_t total_tests = g_integration_results.compatibility_tests + 
                          g_integration_results.stability_tests + 
                          g_integration_results.performance_tests;
    uint32_t total_passed = g_integration_results.compatibility_passed + 
                           g_integration_results.stability_passed + 
                           g_integration_results.performance_passed;
    
    if (total_passed == total_tests)
    {
        my_printf(&huart2, "✓ 所有集成测试通过!\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 有 %lu 个集成测试失败\r\n", total_tests - total_passed);
    }
    
    my_printf(&huart2, "=== BNO080集成测试结束 ===\r\n\r\n");
}

/**
 * @brief  测试应用层接口兼容性
 */
void BNO080_TestApplicationCompatibility(void)
{
    my_printf(&huart2, "\r\n--- 应用层兼容性测试 ---\r\n");
    
    // 测试1: BNO080_Init函数兼容性
    g_integration_results.compatibility_tests++;
    my_printf(&huart2, "测试BNO080_Init函数...\r\n");
    // 函数已在main中调用，这里只验证是否可以重复调用
    BNO080_Init();
    g_integration_results.compatibility_passed++;
    my_printf(&huart2, "✓ BNO080_Init函数兼容性测试通过\r\n");
    
    // 测试2: BNO080_GetData函数兼容性
    g_integration_results.compatibility_tests++;
    BNO080_Data_t test_data1, test_data2;
    
    // 连续调用两次，验证数据结构一致性
    BNO080_GetData(&test_data1);
    HAL_Delay(10);
    BNO080_GetData(&test_data2);
    
    // 验证数据结构大小和字段存在性
    if (sizeof(test_data1) == sizeof(BNO080_Data_t) &&
        sizeof(test_data1.yaw) == sizeof(float) &&
        sizeof(test_data1.pitch) == sizeof(float) &&
        sizeof(test_data1.roll) == sizeof(float) &&
        sizeof(test_data1.new_data_flag) == sizeof(uint8_t))
    {
        g_integration_results.compatibility_passed++;
        my_printf(&huart2, "✓ BNO080_GetData函数兼容性测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ BNO080_GetData函数兼容性测试失败\r\n");
    }
    
    // 测试3: 数据标志位兼容性
    g_integration_results.compatibility_tests++;
    test_data1.new_data_flag = 1;
    test_data1.new_data_flag = 0;  // 模拟应用层清除标志位
    
    // 验证标志位可以正常设置和清除
    if (test_data1.new_data_flag == 0)
    {
        g_integration_results.compatibility_passed++;
        my_printf(&huart2, "✓ 数据标志位兼容性测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据标志位兼容性测试失败\r\n");
    }
    
    // 测试4: 数据范围兼容性
    g_integration_results.compatibility_tests++;
    BNO080_GetData(&test_data1);
    
    // 验证角度数据在合理范围内
    if (test_data1.yaw >= -180.0f && test_data1.yaw <= 180.0f &&
        test_data1.pitch >= -90.0f && test_data1.pitch <= 90.0f &&
        test_data1.roll >= -180.0f && test_data1.roll <= 180.0f)
    {
        g_integration_results.compatibility_passed++;
        my_printf(&huart2, "✓ 数据范围兼容性测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据范围兼容性测试失败\r\n");
        my_printf(&huart2, "  当前数据: Yaw=%.2f, Pitch=%.2f, Roll=%.2f\r\n", 
                 test_data1.yaw, test_data1.pitch, test_data1.roll);
    }
}

/**
 * @brief  测试系统稳定性
 */
void BNO080_TestSystemStability(void)
{
    my_printf(&huart2, "\r\n--- 系统稳定性测试 ---\r\n");
    
    // 测试1: 连续数据读取稳定性
    g_integration_results.stability_tests++;
    uint32_t continuous_reads = 0;
    uint32_t successful_reads = 0;
    uint32_t data_updates = 0;
    
    my_printf(&huart2, "进行连续数据读取测试 (10秒)...\r\n");
    uint32_t test_start = HAL_GetTick();
    
    while (HAL_GetTick() - test_start < 10000)  // 10秒测试
    {
        BNO080_Data_t test_data;
        BNO080_GetData(&test_data);
        continuous_reads++;
        
        // 检查数据是否有效
        if (!isnan(test_data.yaw) && !isnan(test_data.pitch) && !isnan(test_data.roll))
        {
            successful_reads++;
        }
        
        // 检查是否有新数据
        if (test_data.new_data_flag)
        {
            data_updates++;
        }
        
        HAL_Delay(50);  // 20Hz读取频率
    }
    
    float success_rate = (float)successful_reads / continuous_reads * 100.0f;
    
    if (success_rate >= 95.0f)  // 至少95%成功率
    {
        g_integration_results.stability_passed++;
        my_printf(&huart2, "✓ 连续数据读取稳定性测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 连续数据读取稳定性测试失败\r\n");
    }
    
    my_printf(&huart2, "  读取次数: %lu, 成功次数: %lu, 数据更新: %lu\r\n", 
             continuous_reads, successful_reads, data_updates);
    my_printf(&huart2, "  成功率: %.1f%%\r\n", success_rate);
    
    // 测试2: 错误恢复稳定性
    g_integration_results.stability_tests++;
    
    // 清除错误统计
    BNO080_ClearErrorStats();
    
    // 触发一些错误并测试恢复
    BNO080_ErrorHandler(BNO080_ERROR_I2C_TIMEOUT);
    HAL_Delay(100);
    
    // 检查系统是否仍然正常工作
    BNO080_Data_t recovery_test_data;
    BNO080_GetData(&recovery_test_data);
    
    if (!isnan(recovery_test_data.yaw) && !isnan(recovery_test_data.pitch) && !isnan(recovery_test_data.roll))
    {
        g_integration_results.stability_passed++;
        my_printf(&huart2, "✓ 错误恢复稳定性测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 错误恢复稳定性测试失败\r\n");
    }
    
    // 测试3: 内存稳定性
    g_integration_results.stability_tests++;
    
    // 进行大量数据操作，检查是否有内存泄漏或溢出
    for (int i = 0; i < 1000; i++)
    {
        BNO080_Data_t temp_data;
        BNO080_GetData(&temp_data);
        
        BNO080_ErrorStats_t temp_stats;
        BNO080_GetErrorStats(&temp_stats);
        
        if (i % 100 == 0)
        {
            BNO080_ClearErrorStats();
        }
    }
    
    g_integration_results.stability_passed++;
    my_printf(&huart2, "✓ 内存稳定性测试通过\r\n");
}

/**
 * @brief  测试数据精度
 */
void BNO080_TestDataAccuracy(void)
{
    my_printf(&huart2, "\r\n--- 数据精度测试 ---\r\n");
    
    // 测试1: 数据精度验证
    g_integration_results.performance_tests++;
    
    BNO080_Data_t accuracy_data;
    BNO080_GetData(&accuracy_data);
    
    // 检查数据精度是否符合0.01度要求
    // 这里我们检查数据是否有合理的小数位数
    float yaw_frac = accuracy_data.yaw - floorf(accuracy_data.yaw);
    float pitch_frac = accuracy_data.pitch - floorf(accuracy_data.pitch);
    float roll_frac = accuracy_data.roll - floorf(accuracy_data.roll);
    
    // 简单的精度检查：小数部分应该有意义（不全为0）
    if (fabsf(yaw_frac) > 0.001f || fabsf(pitch_frac) > 0.001f || fabsf(roll_frac) > 0.001f)
    {
        g_integration_results.performance_passed++;
        my_printf(&huart2, "✓ 数据精度测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据精度测试失败\r\n");
    }
    
    my_printf(&huart2, "  当前精度: Yaw=%.3f, Pitch=%.3f, Roll=%.3f\r\n", 
             accuracy_data.yaw, accuracy_data.pitch, accuracy_data.roll);
}

/**
 * @brief  测试数据更新频率
 */
void BNO080_TestUpdateFrequency(void)
{
    my_printf(&huart2, "\r\n--- 数据更新频率测试 ---\r\n");
    
    // 测试1: 数据更新频率验证
    g_integration_results.performance_tests++;
    
    uint32_t update_count = 0;
    uint32_t last_update_time = 0;
    
    my_printf(&huart2, "测试数据更新频率 (5秒)...\r\n");
    uint32_t freq_test_start = HAL_GetTick();
    
    while (HAL_GetTick() - freq_test_start < 5000)  // 5秒测试
    {
        BNO080_Data_t freq_data;
        BNO080_GetData(&freq_data);
        
        // 检查是否有新数据更新
        if (freq_data.new_data_flag && freq_data.last_update_time != last_update_time)
        {
            update_count++;
            last_update_time = freq_data.last_update_time;
        }
        
        HAL_Delay(10);  // 100Hz检查频率
    }
    
    float actual_frequency = (float)update_count / 5.0f;  // Hz
    
    // 检查频率是否接近100Hz (允许±20Hz误差)
    if (actual_frequency >= 80.0f && actual_frequency <= 120.0f)
    {
        g_integration_results.performance_passed++;
        my_printf(&huart2, "✓ 数据更新频率测试通过\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据更新频率测试失败\r\n");
    }
    
    my_printf(&huart2, "  实际频率: %.1f Hz (目标: 100 Hz)\r\n", actual_frequency);
    my_printf(&huart2, "  更新次数: %lu (5秒内)\r\n", update_count);
}

/**
 * @brief  获取集成测试结果
 * @param  results: 集成测试结果结构体指针
 */
void BNO080_GetIntegrationTestResults(BNO080_IntegrationTestResults_t* results)
{
    if (results != NULL)
    {
        memcpy(results, &g_integration_results, sizeof(BNO080_IntegrationTestResults_t));
    }
}

/* USER CODE END 1 */