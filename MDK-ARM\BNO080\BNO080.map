Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    startup_stm32f103xb.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(STACK) for __initial_sp
    startup_stm32f103xb.o(RESET) refers to startup_stm32f103xb.o(.text) for Reset_Handler
    startup_stm32f103xb.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f103xb.o(.text) refers to system_stm32f1xx.o(.text.SystemInit) for SystemInit
    startup_stm32f103xb.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(HEAP) for Heap_Mem
    startup_stm32f103xb.o(.text) refers to startup_stm32f103xb.o(STACK) for Stack_Mem
    system_stm32f1xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f1xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f1xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f1xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f1xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f1xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to gpio.o(.text.MX_GPIO_Init) for MX_GPIO_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(.text.main) refers to usart.o(.text.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(.text.main) refers to i2c.o(.text.MX_I2C1_Init) for MX_I2C1_Init
    main.o(.text.main) refers to tim.o(.text.MX_TIM2_Init) for MX_TIM2_Init
    main.o(.text.main) refers to usart.o(.bss.huart2) for huart2
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to usart.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    main.o(.text.main) refers to i2c.o(.bss.hi2c1) for hi2c1
    main.o(.text.main) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.SystemClock_Config) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.ARM.exidx.text.SystemClock_Config) refers to main.o(.text.SystemClock_Config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    gpio.o(.text.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(.text.MX_GPIO_Init) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(.ARM.exidx.text.MX_GPIO_Init) refers to gpio.o(.text.MX_GPIO_Init) for [Anonymous Symbol]
    i2c.o(.text.MX_I2C1_Init) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.MX_I2C1_Init) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(.text.MX_I2C1_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    i2c.o(.ARM.exidx.text.MX_I2C1_Init) refers to i2c.o(.text.MX_I2C1_Init) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(.text.HAL_I2C_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(.text.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    i2c.o(.text.I2C_IsDeviceReady) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    i2c.o(.text.I2C_IsDeviceReady) refers to usart.o(.bss.huart2) for huart2
    i2c.o(.text.I2C_IsDeviceReady) refers to usart.o(.text.my_printf) for my_printf
    i2c.o(.ARM.exidx.text.I2C_IsDeviceReady) refers to i2c.o(.text.I2C_IsDeviceReady) for [Anonymous Symbol]
    i2c.o(.text.I2C_Master_Transmit) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    i2c.o(.text.I2C_Master_Transmit) refers to usart.o(.bss.huart2) for huart2
    i2c.o(.text.I2C_Master_Transmit) refers to usart.o(.text.my_printf) for my_printf
    i2c.o(.ARM.exidx.text.I2C_Master_Transmit) refers to i2c.o(.text.I2C_Master_Transmit) for [Anonymous Symbol]
    i2c.o(.text.I2C_Master_Receive) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    i2c.o(.text.I2C_Master_Receive) refers to usart.o(.bss.huart2) for huart2
    i2c.o(.text.I2C_Master_Receive) refers to usart.o(.text.my_printf) for my_printf
    i2c.o(.ARM.exidx.text.I2C_Master_Receive) refers to i2c.o(.text.I2C_Master_Receive) for [Anonymous Symbol]
    i2c.o(.text.I2C_Mem_Write) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    i2c.o(.text.I2C_Mem_Write) refers to usart.o(.bss.huart2) for huart2
    i2c.o(.text.I2C_Mem_Write) refers to usart.o(.text.my_printf) for my_printf
    i2c.o(.ARM.exidx.text.I2C_Mem_Write) refers to i2c.o(.text.I2C_Mem_Write) for [Anonymous Symbol]
    i2c.o(.text.I2C_Mem_Read) refers to i2c.o(.bss.hi2c1) for hi2c1
    i2c.o(.text.I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    i2c.o(.text.I2C_Mem_Read) refers to usart.o(.bss.huart2) for huart2
    i2c.o(.text.I2C_Mem_Read) refers to usart.o(.text.my_printf) for my_printf
    i2c.o(.ARM.exidx.text.I2C_Mem_Read) refers to i2c.o(.text.I2C_Mem_Read) for [Anonymous Symbol]
    tim.o(.text.MX_TIM2_Init) refers to tim.o(.bss.htim2) for htim2
    tim.o(.text.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(.text.MX_TIM2_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    tim.o(.text.MX_TIM2_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(.text.MX_TIM2_Init) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(.ARM.exidx.text.MX_TIM2_Init) refers to tim.o(.text.MX_TIM2_Init) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(.text.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    tim.o(.text.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.MX_USART1_UART_Init) refers to usart.o(.bss.huart1) for huart1
    usart.o(.text.MX_USART1_UART_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART1_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART1_UART_Init) refers to usart.o(.text.MX_USART1_UART_Init) for [Anonymous Symbol]
    usart.o(.text.MX_USART2_UART_Init) refers to usart.o(.bss.huart2) for huart2
    usart.o(.text.MX_USART2_UART_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    usart.o(.text.MX_USART2_UART_Init) refers to main.o(.text.Error_Handler) for Error_Handler
    usart.o(.ARM.exidx.text.MX_USART2_UART_Init) refers to usart.o(.text.MX_USART2_UART_Init) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(.text.HAL_UART_MspInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to usart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    usart.o(.text.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(.text.my_printf) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    usart.o(.ARM.exidx.text.my_printf) refers to usart.o(.text.my_printf) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_ConfigEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_ConfigEventout) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_EnableEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_EnableEventout) for [Anonymous Symbol]
    stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_DisableEventout) refers to stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_DisableEventout) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init) refers to i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f1xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f1xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f1xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(.text.HAL_Init) refers to stm32f1xx_hal.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f1xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f1xx_hal.o(.text.HAL_InitTick) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f1xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_DeInit) refers to stm32f1xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f1xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f1xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_IncTick) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.text.HAL_IncTick) refers to stm32f1xx_hal.o(.bss.uwTick) for uwTick
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f1xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTick) refers to stm32f1xx_hal.o(.bss.uwTick) for uwTick
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f1xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f1xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f1xx_hal.o(.text.HAL_Delay) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal.o(.text.HAL_Delay) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f1xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f1xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f1xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f1xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f1xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f1xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f1xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(.rodata.cst16) for HAL_RCC_GetSysClockFreq.aPLLMULFactorTable
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(.rodata.HAL_RCC_GetSysClockFreq.aPredivFactorTable) for HAL_RCC_GetSysClockFreq.aPredivFactorTable
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.rodata.cst16) for HAL_RCC_GetSysClockFreq.aPLLMULFactorTable
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.rodata.HAL_RCC_GetSysClockFreq.aPredivFactorTable) for HAL_RCC_GetSysClockFreq.aPredivFactorTable
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f1xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f1xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f1xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f1xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe) for PWR_OverloadWfe
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.PWR_OverloadWfe) refers to stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) for FLASH_PageErase
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for HAL_NVIC_SystemReset
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase) refers to stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase) for HAL_FLASHEx_OBErase
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData) refers to stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetHandle) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetPending) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearPending) for [Anonymous Symbol]
    stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI) refers to stm32f1xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Init) refers to tim.o(.text.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig) refers to stm32f1xx_hal_tim.o(.text.TIM_Base_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) refers to tim.o(.text.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMAError) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(.rodata.cst16) for .Lswitch.table.HAL_TIM_IC_Stop_DMA
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig) refers to stm32f1xx_hal_tim.o(.text.TIM_OC2_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig) refers to stm32f1xx_hal_tim.o(.text.TIM_TI1_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig) refers to stm32f1xx_hal_tim.o(.text.TIM_ETR_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig) refers to stm32f1xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_GetChannelState) for [Anonymous Symbol]
    stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurstState) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(.text.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(.text.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim.o(.text.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim.o(.text.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim.o(.text.TIM_DMAError) for TIM_DMAError
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt) refers to stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState) refers to stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to usart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) refers to usart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAError) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f1xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f1xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to i2c.o(.bss.hi2c1) for hi2c1
    bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) refers to stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for HAL_I2C_IsDeviceReady
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_IsReady) refers to bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_Init) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_Init) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.text.BNO080_Init) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.text.BNO080_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    bno080_hal_fixed.o(.text.BNO080_Init) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.31
    bno080_hal_fixed.o(.text.BNO080_Init) refers to bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) for BNO080_I2C_IsReady
    bno080_hal_fixed.o(.text.BNO080_Init) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_Init) refers to bno080_hal_fixed.o(.text.BNO080_Init) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_GPIO_Config) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_GPIO_Config) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.31
    bno080_hal_fixed.o(.text.BNO080_GPIO_Config) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GPIO_Config) refers to bno080_hal_fixed.o(.text.BNO080_GPIO_Config) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_Reset) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_Reset) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.20
    bno080_hal_fixed.o(.text.BNO080_I2C_Reset) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.text.BNO080_I2C_Reset) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_Reset) refers to bno080_hal_fixed.o(.text.BNO080_I2C_Reset) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.22
    bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_CheckStatus) refers to bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.27
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ConfigureRotationVector) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureGameRotationVector) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureGameRotationVector) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.29
    bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureGameRotationVector) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ConfigureGameRotationVector) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureGameRotationVector) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_GetData) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GetData) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID) refers to bno080_hal_fixed.o(.rodata.str1.1) for .L.str.25
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ReadProductID) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_ErrorHandler) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.text.BNO080_ErrorHandler) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_ErrorHandler) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_ErrorHandler) refers to bno080_hal_fixed.o(.text.BNO080_ErrorHandler) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_ClearErrorStats) refers to bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_GetErrorStats) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GetErrorStats) refers to bno080_hal_fixed.o(.text.BNO080_GetErrorStats) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_IsDeviceOnline) refers to bno080_hal_fixed.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_IsDeviceOnline) refers to bno080_hal_fixed.o(.text.BNO080_IsDeviceOnline) for [Anonymous Symbol]
    bno080_hal_fixed.o(.text.BNO080_I2C_ReadSensorData) refers to usart.o(.bss.huart2) for huart2
    bno080_hal_fixed.o(.text.BNO080_I2C_ReadSensorData) refers to usart.o(.text.my_printf) for my_printf
    bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ReadSensorData) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ReadSensorData) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to usart.o(.bss.huart2) for huart2
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.rodata.str1.1) for .L.str
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to usart.o(.text.my_printf) for my_printf
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) for BNO080_TestApplicationCompatibility
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.text.BNO080_TestSystemStability) for BNO080_TestSystemStability
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.text.BNO080_TestDataAccuracy) for BNO080_TestDataAccuracy
    bno080_integration_test.o(.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) for BNO080_TestUpdateFrequency
    bno080_integration_test.o(.ARM.exidx.text.BNO080_RunIntegrationTests) refers to bno080_integration_test.o(.text.BNO080_RunIntegrationTests) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to usart.o(.bss.huart2) for huart2
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to bno080_integration_test.o(.rodata.str1.1) for .L.str.9
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to usart.o(.text.my_printf) for my_printf
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to bno080_hal_fixed.o(.text.BNO080_Init) for BNO080_Init
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for BNO080_GetData
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno080_integration_test.o(.ARM.exidx.text.BNO080_TestApplicationCompatibility) refers to bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to usart.o(.bss.huart2) for huart2
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to usart.o(.text.my_printf) for my_printf
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for BNO080_GetData
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_integration_test.o(.rodata.str1.1) for .L.str.20
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) for BNO080_ClearErrorStats
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_hal_fixed.o(.text.BNO080_ErrorHandler) for BNO080_ErrorHandler
    bno080_integration_test.o(.text.BNO080_TestSystemStability) refers to bno080_hal_fixed.o(.text.BNO080_GetErrorStats) for BNO080_GetErrorStats
    bno080_integration_test.o(.ARM.exidx.text.BNO080_TestSystemStability) refers to bno080_integration_test.o(.text.BNO080_TestSystemStability) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to usart.o(.bss.huart2) for huart2
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to usart.o(.text.my_printf) for my_printf
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for BNO080_GetData
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to floorf.o(i.floorf) for floorf
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno080_integration_test.o(.text.BNO080_TestDataAccuracy) refers to bno080_integration_test.o(.rodata.str1.1) for .L.str.30
    bno080_integration_test.o(.ARM.exidx.text.BNO080_TestDataAccuracy) refers to bno080_integration_test.o(.text.BNO080_TestDataAccuracy) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to usart.o(.bss.huart2) for huart2
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to usart.o(.text.my_printf) for my_printf
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for BNO080_GetData
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to bno080_integration_test.o(.rodata.str1.1) for .L.str.33
    bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno080_integration_test.o(.ARM.exidx.text.BNO080_TestUpdateFrequency) refers to bno080_integration_test.o(.text.BNO080_TestUpdateFrequency) for [Anonymous Symbol]
    bno080_integration_test.o(.text.BNO080_GetIntegrationTestResults) refers to bno080_integration_test.o(.bss.g_integration_results) for g_integration_results
    bno080_integration_test.o(.ARM.exidx.text.BNO080_GetIntegrationTestResults) refers to bno080_integration_test.o(.text.BNO080_GetIntegrationTestResults) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_RunAllTests) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    bno080_test.o(.text.BNO080_RunAllTests) refers to usart.o(.bss.huart2) for huart2
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.rodata.str1.1) for .L.str
    bno080_test.o(.text.BNO080_RunAllTests) refers to usart.o(.text.my_printf) for my_printf
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.text.BNO080_TestI2CCommunication) for BNO080_TestI2CCommunication
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.text.BNO080_TestDataConversion) for BNO080_TestDataConversion
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.text.BNO080_TestErrorHandling) for BNO080_TestErrorHandling
    bno080_test.o(.text.BNO080_RunAllTests) refers to bno080_test.o(.text.BNO080_TestPerformance) for BNO080_TestPerformance
    bno080_test.o(.ARM.exidx.text.BNO080_RunAllTests) refers to bno080_test.o(.text.BNO080_RunAllTests) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_ClearTestResults) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.ARM.exidx.text.BNO080_ClearTestResults) refers to bno080_test.o(.text.BNO080_ClearTestResults) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to usart.o(.bss.huart2) for huart2
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_test.o(.rodata.str1.1) for .L.str.9
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to usart.o(.text.my_printf) for my_printf
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_hal_fixed.o(.text.BNO080_I2C_IsReady) for BNO080_I2C_IsReady
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_hal_fixed.o(.text.BNO080_I2C_Reset) for BNO080_I2C_Reset
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus) for BNO080_I2C_CheckStatus
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID) for BNO080_I2C_ReadProductID
    bno080_test.o(.text.BNO080_TestI2CCommunication) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector) for BNO080_I2C_ConfigureRotationVector
    bno080_test.o(.ARM.exidx.text.BNO080_TestI2CCommunication) refers to bno080_test.o(.text.BNO080_TestI2CCommunication) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestDataConversion) refers to usart.o(.bss.huart2) for huart2
    bno080_test.o(.text.BNO080_TestDataConversion) refers to usart.o(.text.my_printf) for my_printf
    bno080_test.o(.text.BNO080_TestDataConversion) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.text.BNO080_TestDataConversion) refers to bno080_test.o(.rodata.str1.1) for .L.str.21
    bno080_test.o(.text.BNO080_TestDataConversion) refers to bno080_test.o(.text.BNO080_TestQuaternionToEuler) for BNO080_TestQuaternionToEuler
    bno080_test.o(.ARM.exidx.text.BNO080_TestDataConversion) refers to bno080_test.o(.text.BNO080_TestDataConversion) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to usart.o(.bss.huart2) for huart2
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to usart.o(.text.my_printf) for my_printf
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_hal_fixed.o(.text.BNO080_ClearErrorStats) for BNO080_ClearErrorStats
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_hal_fixed.o(.text.BNO080_GetErrorStats) for BNO080_GetErrorStats
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_hal_fixed.o(.text.BNO080_ErrorHandler) for BNO080_ErrorHandler
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_hal_fixed.o(.text.BNO080_IsDeviceOnline) for BNO080_IsDeviceOnline
    bno080_test.o(.text.BNO080_TestErrorHandling) refers to bno080_test.o(.rodata.str1.1) for .L.str.34
    bno080_test.o(.ARM.exidx.text.BNO080_TestErrorHandling) refers to bno080_test.o(.text.BNO080_TestErrorHandling) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestPerformance) refers to usart.o(.bss.huart2) for huart2
    bno080_test.o(.text.BNO080_TestPerformance) refers to usart.o(.text.my_printf) for my_printf
    bno080_test.o(.text.BNO080_TestPerformance) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.text.BNO080_TestPerformance) refers to stm32f1xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    bno080_test.o(.text.BNO080_TestPerformance) refers to bno080_hal_fixed.o(.text.BNO080_I2C_ReadSensorData) for BNO080_I2C_ReadSensorData
    bno080_test.o(.text.BNO080_TestPerformance) refers to stm32f1xx_hal.o(.text.HAL_Delay) for HAL_Delay
    bno080_test.o(.text.BNO080_TestPerformance) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    bno080_test.o(.text.BNO080_TestPerformance) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    bno080_test.o(.text.BNO080_TestPerformance) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    bno080_test.o(.text.BNO080_TestPerformance) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    bno080_test.o(.text.BNO080_TestPerformance) refers to bno080_test.o(.rodata.str1.1) for .L.str.39
    bno080_test.o(.text.BNO080_TestPerformance) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno080_test.o(.text.BNO080_TestPerformance) refers to bno080_hal_fixed.o(.text.BNO080_GetData) for BNO080_GetData
    bno080_test.o(.ARM.exidx.text.BNO080_TestPerformance) refers to bno080_test.o(.text.BNO080_TestPerformance) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to sqrtf.o(i.sqrtf) for sqrtf
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmple
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to atan2f.o(i.atan2f) for atan2f
    bno080_test.o(.text.BNO080_TestQuaternionToEuler) refers to asinf.o(i.asinf) for asinf
    bno080_test.o(.ARM.exidx.text.BNO080_TestQuaternionToEuler) refers to bno080_test.o(.text.BNO080_TestQuaternionToEuler) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_GetTestResults) refers to bno080_test.o(.bss.g_test_results) for g_test_results
    bno080_test.o(.ARM.exidx.text.BNO080_GetTestResults) refers to bno080_test.o(.text.BNO080_GetTestResults) for [Anonymous Symbol]
    bno080_test.o(.text.BNO080_TestFloatEqual) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    bno080_test.o(.text.BNO080_TestFloatEqual) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmple
    bno080_test.o(.ARM.exidx.text.BNO080_TestFloatEqual) refers to bno080_test.o(.text.BNO080_TestFloatEqual) for [Anonymous Symbol]
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    asinf.o(i.__softfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.asinf) for asinf
    asinf.o(i.asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.asinf) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    asinf.o(i.asinf) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    asinf.o(i.asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.asinf) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    asinf.o(i.asinf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    asinf.o(i.asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.asinf) refers to _rserrno.o(.text) for __set_errno
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    atan2f.o(i.atan2f) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to faddsub_clz.o(x$fpl$frsb) for __aeabi_frsub
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    floorf.o(i.__softfp_floorf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floorf.o(i.__softfp_floorf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    floorf.o(i.floorf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    floorf.o(i.floorf) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(x$fpl$fsqrt) for _fsqrt
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fsqrt.o(x$fpl$fsqrt) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    scalbnf.o(x$fpl$scalbnf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbnf.o(x$fpl$scalbnf) refers to fcheck1.o(x$fpl$fcheck1) for __fpl_fcheck_NaN1
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to scalbnf.o(x$fpl$scalbnf) for __ARM_scalbnf
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    fcheck1.o(x$fpl$fcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcheck1.o(x$fpl$fcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f103xb.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f1xx.o(.text), (0 bytes).
    Removing system_stm32f1xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f1xx.o(.text.SystemCoreClockUpdate), (92 bytes).
    Removing system_stm32f1xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.text.SystemClock_Config), (100 bytes).
    Removing main.o(.ARM.exidx.text.SystemClock_Config), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.bss.bno080_data), (24 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing gpio.o(.text), (0 bytes).
    Removing gpio.o(.ARM.exidx.text.MX_GPIO_Init), (8 bytes).
    Removing i2c.o(.text), (0 bytes).
    Removing i2c.o(.ARM.exidx.text.MX_I2C1_Init), (8 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing i2c.o(.text.HAL_I2C_MspDeInit), (70 bytes).
    Removing i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing i2c.o(.text.I2C_IsDeviceReady), (144 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_IsDeviceReady), (8 bytes).
    Removing i2c.o(.text.I2C_Master_Transmit), (88 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_Master_Transmit), (8 bytes).
    Removing i2c.o(.text.I2C_Master_Receive), (88 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_Master_Receive), (8 bytes).
    Removing i2c.o(.text.I2C_Mem_Write), (104 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_Mem_Write), (8 bytes).
    Removing i2c.o(.text.I2C_Mem_Read), (104 bytes).
    Removing i2c.o(.ARM.exidx.text.I2C_Mem_Read), (8 bytes).
    Removing tim.o(.text), (0 bytes).
    Removing tim.o(.ARM.exidx.text.MX_TIM2_Init), (8 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing tim.o(.text.HAL_TIM_Base_MspDeInit), (32 bytes).
    Removing tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART1_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.MX_USART2_UART_Init), (8 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing usart.o(.text.HAL_UART_MspDeInit), (102 bytes).
    Removing usart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing usart.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_ConfigEventout), (20 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_ConfigEventout), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_EnableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_EnableEventout), (8 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.text.HAL_GPIOEx_DisableEventout), (16 bytes).
    Removing stm32f1xx_hal_gpio_ex.o(.ARM.exidx.text.HAL_GPIOEx_DisableEventout), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (1028 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (364 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (376 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (472 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_DMAError), (58 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (468 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (272 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (272 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (846 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (260 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (272 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (518 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (580 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (500 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_DMAAbort), (290 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1606 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (326 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (340 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f1xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f1xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f1xx_hal.o(.text), (0 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DeInit), (32 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_IncTick), (26 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f1xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f1xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_DeInit), (234 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq), (86 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (82 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (124 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (66 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f1xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (258 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (38 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (248 bytes).
    Removing stm32f1xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (276 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f1xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Init), (132 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_DeInit), (100 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Start), (116 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Start_IT), (160 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort), (70 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (182 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (1458 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (416 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (46 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (122 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f1xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f1xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f1xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (22 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (22 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (30 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.PWR_OverloadWfe), (6 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.PWR_OverloadWfe), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f1xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program), (278 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (198 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (368 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (4 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f1xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f1xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (238 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.FLASH_PageErase), (40 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_PageErase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (98 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBErase), (172 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBErase), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (694 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (42 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetUserData), (34 bytes).
    Removing stm32f1xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetUserData), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text), (0 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_SetConfigLine), (186 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_SetConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetConfigLine), (148 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearConfigLine), (140 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearConfigLine), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_RegisterCallback), (12 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_RegisterCallback), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetHandle), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_IRQHandler), (42 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GetPending), (28 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GetPending), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_ClearPending), (24 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_ClearPending), (8 bytes).
    Removing stm32f1xx_hal_exti.o(.text.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f1xx_hal_exti.o(.ARM.exidx.text.HAL_EXTI_GenerateSWI), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text), (0 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_Base_SetConfig), (174 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_Base_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start), (102 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop), (52 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_IT), (110 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop_IT), (60 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Start_DMA), (188 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedCplt), (20 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMAPeriodElapsedHalfCplt), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAPeriodElapsedHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMAError), (94 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMAError), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Stop_DMA), (72 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Init), (250 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start), (218 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_CCxChannelCmd), (36 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_CCxChannelCmd), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop), (178 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_IT), (282 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Start_DMA), (566 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseCplt), (106 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMADelayPulseHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_Stop_DMA), (192 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Init), (250 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start), (218 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop), (178 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_IT), (282 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_IT), (182 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Start_DMA), (566 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_Stop_DMA), (192 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Init), (250 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_DeInit), (96 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start), (234 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop), (126 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_IT), (294 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_IT), (150 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Start_DMA), (526 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMACaptureCplt), (118 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMACaptureHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_Stop_DMA), (162 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Init), (254 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_DeInit), (80 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start), (128 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop), (128 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Start_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Init), (346 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Init), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_DeInit), (80 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start), (188 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop), (130 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_IT), (230 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_IT), (214 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Start_DMA), (552 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_Stop_DMA), (230 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IRQHandler), (334 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_DelayElapsedCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_ConfigChannel), (420 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_ConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_OC2_SetConfig), (96 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_OC2_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_ConfigChannel), (386 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_ConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_TI1_SetConfig), (114 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_TI1_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_ConfigChannel), (516 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_ConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_ConfigChannel), (478 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_ConfigChannel), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStart), (28 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStart), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiWriteStart), (644 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiWriteStart), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMATriggerCplt), (20 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_DMATriggerHalfCplt), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_DMATriggerHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_WriteStop), (118 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_WriteStop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStart), (28 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStart), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_MultiReadStart), (628 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_MultiReadStart), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurst_ReadStop), (118 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurst_ReadStop), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_GenerateEvent), (36 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GenerateEvent), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigOCrefClear), (212 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigOCrefClear), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_ETR_SetConfig), (22 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_ETR_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigClockSource), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ConfigTI1Input), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro), (88 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.TIM_SlaveTimer_SetConfig), (250 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.TIM_SlaveTimer_SetConfig), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_SlaveConfigSynchro_IT), (88 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_SlaveConfigSynchro_IT), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_ReadCapturedValue), (22 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ReadCapturedValue), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PeriodElapsedHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_CaptureHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_TriggerHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetActiveChannel), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_GetChannelState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.text.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f1xx_hal_tim.o(.ARM.exidx.text.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f1xx_hal_tim.o(.rodata.cst16), (48 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text), (0 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Init), (200 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Init), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspInit), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_DeInit), (80 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_DeInit), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start), (162 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop), (66 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_IT), (170 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Start_DMA), (214 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_Stop_DMA), (74 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start), (194 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop), (146 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_IT), (214 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_IT), (234 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Start_DMA), (462 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMADelayPulseNCplt), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.TIM_DMAErrorCCxN), (70 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIM_DMAErrorCCxN), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OCN_Stop_DMA), (176 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OCN_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start), (194 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop), (146 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_IT), (214 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_IT), (234 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Start_DMA), (462 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Start_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_PWMN_Stop_DMA), (176 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_PWMN_Stop_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start), (126 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop), (128 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Start_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_OnePulseN_Stop_IT), (144 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_OnePulseN_Stop_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_IT), (118 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_IT), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigCommutEvent_DMA), (154 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigCommutEvent_DMA), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationCplt), (12 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationCplt), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.TIMEx_DMACommutationHalfCplt), (12 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.TIMEx_DMACommutationHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_MasterConfigSynchronization), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_ConfigBreakDeadTime), (76 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_ConfigBreakDeadTime), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_RemapConfig), (4 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_RemapConfig), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutCallback), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_CommutHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_BreakCallback), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f1xx_hal_tim_ex.o(.ARM.exidx.text.HAL_TIMEx_GetChannelNState), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_MspInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (234 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_LIN_Init), (248 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (260 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (194 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMAError), (380 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (364 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_Start_Receive_DMA), (316 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_DMAStop), (540 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (484 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_IRQHandler), (1392 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_Receive_IT), (254 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMAAbortOnError), (12 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMAReceiveCplt), (350 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f1xx_hal_uart.o(.text.UART_DMARxHalfCplt), (24 bytes).
    Removing stm32f1xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing bno080_hal_fixed.o(.text), (0 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_IsReady), (408 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_IsReady), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_Init), (476 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_Init), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_GPIO_Config), (110 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GPIO_Config), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_Reset), (48 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_Reset), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_CheckStatus), (42 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_CheckStatus), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureRotationVector), (44 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ConfigureRotationVector), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_ConfigureGameRotationVector), (96 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ConfigureGameRotationVector), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_GetData), (46 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GetData), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_ReadProductID), (144 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ReadProductID), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_ErrorHandler), (236 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_ErrorHandler), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_ClearErrorStats), (80 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_ClearErrorStats), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_GetErrorStats), (66 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_GetErrorStats), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_IsDeviceOnline), (12 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_IsDeviceOnline), (8 bytes).
    Removing bno080_hal_fixed.o(.text.BNO080_I2C_ReadSensorData), (52 bytes).
    Removing bno080_hal_fixed.o(.ARM.exidx.text.BNO080_I2C_ReadSensorData), (8 bytes).
    Removing bno080_hal_fixed.o(.rodata.str1.1), (979 bytes).
    Removing bno080_hal_fixed.o(.bss..L_MergedGlobals), (64 bytes).
    Removing bno080_integration_test.o(.text), (0 bytes).
    Removing bno080_integration_test.o(.text.BNO080_RunIntegrationTests), (356 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_RunIntegrationTests), (8 bytes).
    Removing bno080_integration_test.o(.text.BNO080_TestApplicationCompatibility), (496 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_TestApplicationCompatibility), (8 bytes).
    Removing bno080_integration_test.o(.text.BNO080_TestSystemStability), (648 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_TestSystemStability), (8 bytes).
    Removing bno080_integration_test.o(.text.BNO080_TestDataAccuracy), (332 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_TestDataAccuracy), (8 bytes).
    Removing bno080_integration_test.o(.text.BNO080_TestUpdateFrequency), (384 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_TestUpdateFrequency), (8 bytes).
    Removing bno080_integration_test.o(.text.BNO080_GetIntegrationTestResults), (46 bytes).
    Removing bno080_integration_test.o(.ARM.exidx.text.BNO080_GetIntegrationTestResults), (8 bytes).
    Removing bno080_integration_test.o(.rodata.str1.1), (838 bytes).
    Removing bno080_integration_test.o(.bss.g_integration_results), (28 bytes).
    Removing bno080_test.o(.text), (0 bytes).
    Removing bno080_test.o(.text.BNO080_RunAllTests), (324 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_RunAllTests), (8 bytes).
    Removing bno080_test.o(.text.BNO080_ClearTestResults), (20 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_ClearTestResults), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestI2CCommunication), (460 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestI2CCommunication), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestDataConversion), (404 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestDataConversion), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestErrorHandling), (396 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestErrorHandling), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestPerformance), (424 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestPerformance), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestQuaternionToEuler), (464 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestQuaternionToEuler), (8 bytes).
    Removing bno080_test.o(.text.BNO080_GetTestResults), (28 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_GetTestResults), (8 bytes).
    Removing bno080_test.o(.text.BNO080_TestFloatEqual), (26 bytes).
    Removing bno080_test.o(.ARM.exidx.text.BNO080_TestFloatEqual), (8 bytes).
    Removing bno080_test.o(.rodata.str1.1), (713 bytes).
    Removing bno080_test.o(.bss.g_test_results), (16 bytes).

901 unused section(s) (total 69908 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcheck1.s                       0x00000000   Number         0  fcheck1.o ABSOLUTE
    ../fplib/fcmp.s                          0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fgeqf.s                         0x00000000   Number         0  fgeqf.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fpinit_empty.s                  0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/fsqrt.s                         0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbnf.s                       0x00000000   Number         0  scalbnf.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/floorf.c                      0x00000000   Number         0  floorf.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    bno080_hal_fixed.c                       0x00000000   Number         0  bno080_hal_fixed.o ABSOLUTE
    bno080_integration_test.c                0x00000000   Number         0  bno080_integration_test.o ABSOLUTE
    bno080_test.c                            0x00000000   Number         0  bno080_test.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    gpio.c                                   0x00000000   Number         0  gpio.o ABSOLUTE
    i2c.c                                    0x00000000   Number         0  i2c.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f103xb.s                    0x00000000   Number         0  startup_stm32f103xb.o ABSOLUTE
    stm32f1xx_hal.c                          0x00000000   Number         0  stm32f1xx_hal.o ABSOLUTE
    stm32f1xx_hal_cortex.c                   0x00000000   Number         0  stm32f1xx_hal_cortex.o ABSOLUTE
    stm32f1xx_hal_dma.c                      0x00000000   Number         0  stm32f1xx_hal_dma.o ABSOLUTE
    stm32f1xx_hal_exti.c                     0x00000000   Number         0  stm32f1xx_hal_exti.o ABSOLUTE
    stm32f1xx_hal_flash.c                    0x00000000   Number         0  stm32f1xx_hal_flash.o ABSOLUTE
    stm32f1xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f1xx_hal_flash_ex.o ABSOLUTE
    stm32f1xx_hal_gpio.c                     0x00000000   Number         0  stm32f1xx_hal_gpio.o ABSOLUTE
    stm32f1xx_hal_gpio_ex.c                  0x00000000   Number         0  stm32f1xx_hal_gpio_ex.o ABSOLUTE
    stm32f1xx_hal_i2c.c                      0x00000000   Number         0  stm32f1xx_hal_i2c.o ABSOLUTE
    stm32f1xx_hal_pwr.c                      0x00000000   Number         0  stm32f1xx_hal_pwr.o ABSOLUTE
    stm32f1xx_hal_rcc.c                      0x00000000   Number         0  stm32f1xx_hal_rcc.o ABSOLUTE
    stm32f1xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f1xx_hal_rcc_ex.o ABSOLUTE
    stm32f1xx_hal_tim.c                      0x00000000   Number         0  stm32f1xx_hal_tim.o ABSOLUTE
    stm32f1xx_hal_tim_ex.c                   0x00000000   Number         0  stm32f1xx_hal_tim_ex.o ABSOLUTE
    stm32f1xx_hal_uart.c                     0x00000000   Number         0  stm32f1xx_hal_uart.o ABSOLUTE
    system_stm32f1xx.c                       0x00000000   Number         0  system_stm32f1xx.o ABSOLUTE
    tim.c                                    0x00000000   Number         0  tim.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f103xb.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000150   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x0800016c   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x08000170   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800018c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800018c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000192   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000198   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800019e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x080001a4   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x080001aa   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x080001b0   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x080001ba   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x080001c0   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080001c6   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001cc   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001d2   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001d8   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001de   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001e4   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x080001ea   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080001f0   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080001f6   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000200   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000206   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800020c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000212   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000218   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800021c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x0800021e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x0800021e   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x08000224   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x08000224   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x08000230   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x08000230   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x0800023a   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x0800023a   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x0800023c   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x0800023e   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x0800023e   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x08000240   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000240   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000240   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000246   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000246   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800024a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800024a   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000252   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000254   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000254   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000258   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000260   Section       64  startup_stm32f103xb.o(.text)
    .text                                    0x080002a0   Section        0  vsnprintf.o(.text)
    .text                                    0x080002dc   Section        0  heapauxi.o(.text)
    .text                                    0x080002e2   Section        0  _printf_pad.o(.text)
    .text                                    0x08000330   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000354   Section        0  _printf_str.o(.text)
    .text                                    0x080003a8   Section        0  _printf_dec.o(.text)
    .text                                    0x08000420   Section        0  _printf_charcount.o(.text)
    _printf_input_char                       0x08000449   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000448   Section        0  _printf_char_common.o(.text)
    .text                                    0x08000478   Section        0  _snputc.o(.text)
    .text                                    0x08000488   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000544   Section        0  _printf_longlong_dec.o(.text)
    _printf_longlong_oct_internal            0x080005c1   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x080005c0   Section        0  _printf_oct_int_ll.o(.text)
    _printf_hex_common                       0x08000631   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000630   Section        0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080006c4   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x0800084c   Section      138  lludiv10.o(.text)
    .text                                    0x080008d6   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x08000989   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08000988   Section        0  _printf_fp_dec.o(.text)
    .text                                    0x08000da4   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080010a0   Section        0  _printf_char.o(.text)
    .text                                    0x080010cc   Section        0  _printf_wchar.o(.text)
    .text                                    0x080010f8   Section        0  _c16rtomb.o(.text)
    .text                                    0x08001140   Section        8  libspace.o(.text)
    .text                                    0x08001148   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001194   Section       16  rt_ctype_table.o(.text)
    .text                                    0x080011a4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x080011ac   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x0800122c   Section        0  bigflt0.o(.text)
    .text                                    0x08001310   Section        0  exit.o(.text)
    .text                                    0x08001324   Section      128  strcmpv7m.o(.text)
    .text                                    0x080013a4   Section        0  sys_exit.o(.text)
    .text                                    0x080013b0   Section        2  use_no_semi.o(.text)
    .text                                    0x080013b2   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x080013b4   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080013c0   Section        0  stm32f1xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x080013e8   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080015f8   Section        0  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08001604   Section        0  stm32f1xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08001610   Section        0  stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init)
    [Anonymous Symbol]                       0x08001774   Section        0  i2c.o(.text.HAL_I2C_MspInit)
    [Anonymous Symbol]                       0x08001800   Section        0  stm32f1xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001828   Section        0  stm32f1xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001870   Section        0  stm32f1xx_hal.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x08001874   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001898   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x080018f0   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001910   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001acc   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08001af4   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001b1c   Section        0  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001f1c   Section        0  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001f48   Section        0  stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    [Anonymous Symbol]                       0x08001fc8   Section        0  stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    [Anonymous Symbol]                       0x080020c4   Section        0  tim.o(.text.HAL_TIM_Base_MspInit)
    [Anonymous Symbol]                       0x08002104   Section        0  stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    [Anonymous Symbol]                       0x080022a4   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08002388   Section        0  usart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08002484   Section        0  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x08002618   Section        0  gpio.o(.text.MX_GPIO_Init)
    [Anonymous Symbol]                       0x08002690   Section        0  i2c.o(.text.MX_I2C1_Init)
    [Anonymous Symbol]                       0x080026d4   Section        0  tim.o(.text.MX_TIM2_Init)
    [Anonymous Symbol]                       0x08002744   Section        0  usart.o(.text.MX_USART1_UART_Init)
    [Anonymous Symbol]                       0x08002780   Section        0  usart.o(.text.MX_USART2_UART_Init)
    [Anonymous Symbol]                       0x080027bc   Section        0  system_stm32f1xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x0800280c   Section        0  main.o(.text.main)
    [Anonymous Symbol]                       0x080029a0   Section        0  usart.o(.text.my_printf)
    CL$$btod_d2e                             0x080029de   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08002a1c   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002a62   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002ac4   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002dfc   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002ed8   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002f02   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08002f2c   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08003170   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08003198   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x080031a8   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080031d4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$printf1                            0x08003200   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08003204   Section        4  printf2.o(x$fpl$printf2)
    initial_mbstate                          0x08003208   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08003208   Section        8  _printf_wctomb.o(.constdata)
    x$fpl$usenofp                            0x08003208   Section        0  usenofp.o(x$fpl$usenofp)
    uc_hextab                                0x08003210   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08003210   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08003224   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    maptable                                 0x08003238   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08003238   Section       17  __printf_flags_ss_wp.o(.constdata)
    lc_hextab                                0x08003249   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08003249   Section       38  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800325c   Data          19  _printf_fp_hex.o(.constdata)
    tenpwrs_x                                0x08003270   Data          60  bigflt0.o(.constdata)
    .constdata                               0x08003270   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x080032ac   Data          64  bigflt0.o(.constdata)
    HAL_RCC_GetSysClockFreq.aPredivFactorTable 0x0800331c   Data           2  stm32f1xx_hal_rcc.o(.rodata.HAL_RCC_GetSysClockFreq.aPredivFactorTable)
    [Anonymous Symbol]                       0x0800331c   Section        0  stm32f1xx_hal_rcc.o(.rodata.HAL_RCC_GetSysClockFreq.aPredivFactorTable)
    HAL_RCC_GetSysClockFreq.aPLLMULFactorTable 0x0800331e   Data          16  stm32f1xx_hal_rcc.o(.rodata.cst16)
    [Anonymous Symbol]                       0x0800331e   Section        0  stm32f1xx_hal_rcc.o(.rodata.cst16)
    .L.str.4                                 0x0800332e   Data          28  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800332e   Section        0  main.o(.rodata.str1.1)
    .L.str                                   0x0800334a   Data          29  main.o(.rodata.str1.1)
    .L.str.1                                 0x08003367   Data          43  main.o(.rodata.str1.1)
    .L.str.8                                 0x08003392   Data          37  main.o(.rodata.str1.1)
    .L.str.2                                 0x080033b7   Data          22  main.o(.rodata.str1.1)
    locale$$data                             0x080033f0   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080033f4   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080033fc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08003408   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800340a   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800340b   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x0800340c   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800340c   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08003410   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08003418   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800351c   Data           0  lc_ctype_c.o(locale$$data)
    .L_MergedGlobals                         0x20000000   Data           8  stm32f1xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000000   Section        0  stm32f1xx_hal.o(.data..L_MergedGlobals)
    .bss                                     0x20000010   Section       96  libspace.o(.bss)
    Heap_Mem                                 0x200001a0   Data         512  startup_stm32f103xb.o(HEAP)
    HEAP                                     0x200001a0   Section      512  startup_stm32f103xb.o(HEAP)
    Stack_Mem                                0x200003a0   Data        1024  startup_stm32f103xb.o(STACK)
    STACK                                    0x200003a0   Section     1024  startup_stm32f103xb.o(STACK)
    __initial_sp                             0x200007a0   Data           0  startup_stm32f103xb.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __fp_init_empty                          0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f103xb.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f103xb.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f103xb.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x080000ff   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000151   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x0800016d   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x08000171   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800018d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800018d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000193   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000199   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800019f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x080001a5   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x080001ab   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x080001b1   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x080001bb   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x080001c1   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080001c7   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001cd   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001d3   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001d9   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001df   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001e5   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x080001eb   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080001f1   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080001f7   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000201   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000207   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800020d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000213   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000219   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800021d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x0800021f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x08000225   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x08000231   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x0800023b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x0800023d   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x0800023f   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x08000241   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000241   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000241   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000247   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000247   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800024b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000253   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000255   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000255   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000259   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000261   Thumb Code     8  startup_stm32f103xb.o(.text)
    NMI_Handler                              0x08000269   Thumb Code     2  startup_stm32f103xb.o(.text)
    HardFault_Handler                        0x0800026b   Thumb Code     2  startup_stm32f103xb.o(.text)
    MemManage_Handler                        0x0800026d   Thumb Code     2  startup_stm32f103xb.o(.text)
    BusFault_Handler                         0x0800026f   Thumb Code     2  startup_stm32f103xb.o(.text)
    UsageFault_Handler                       0x08000271   Thumb Code     2  startup_stm32f103xb.o(.text)
    SVC_Handler                              0x08000273   Thumb Code     2  startup_stm32f103xb.o(.text)
    DebugMon_Handler                         0x08000275   Thumb Code     2  startup_stm32f103xb.o(.text)
    PendSV_Handler                           0x08000277   Thumb Code     2  startup_stm32f103xb.o(.text)
    SysTick_Handler                          0x08000279   Thumb Code     2  startup_stm32f103xb.o(.text)
    ADC1_2_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_RX1_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    CAN1_SCE_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel1_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel2_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel3_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel4_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel5_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel6_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    DMA1_Channel7_IRQHandler                 0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI0_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI15_10_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI1_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI2_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI3_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI4_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    EXTI9_5_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    FLASH_IRQHandler                         0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C1_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_ER_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    I2C2_EV_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    PVD_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RCC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_Alarm_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    RTC_IRQHandler                           0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI1_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    SPI2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TAMPER_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_BRK_IRQHandler                      0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_CC_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM1_UP_IRQHandler                       0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM2_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM3_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    TIM4_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART1_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART2_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USART3_IRQHandler                        0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USBWakeUp_IRQHandler                     0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    WWDG_IRQHandler                          0x0800027b   Thumb Code     0  startup_stm32f103xb.o(.text)
    __user_initial_stackheap                 0x0800027d   Thumb Code     0  startup_stm32f103xb.o(.text)
    vsnprintf                                0x080002a1   Thumb Code    56  vsnprintf.o(.text)
    __use_two_region_memory                  0x080002dd   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080002df   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080002e1   Thumb Code     2  heapauxi.o(.text)
    _printf_pre_padding                      0x080002e3   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800030f   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000331   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000343   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000355   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080003a9   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000421   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000453   Thumb Code    32  _printf_char_common.o(.text)
    _snputc                                  0x08000479   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000489   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000545   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080005c1   Thumb Code    68  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000605   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x0800061d   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000631   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000687   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080006a3   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080006af   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080006c5   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _ll_udiv10                               0x0800084d   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x080008d7   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x08000b39   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08000da5   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x080010a1   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x080010b5   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x080010c5   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x080010cd   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x080010e1   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x080010f1   Thumb Code     8  _printf_wchar.o(.text)
    _c16rtomb                                0x080010f9   Thumb Code    72  _c16rtomb.o(.text)
    _wcrtomb                                 0x080010f9   Thumb Code     0  _c16rtomb.o(.text)
    __user_libspace                          0x08001141   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08001141   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08001141   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x08001149   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001195   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x080011a5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x080011ad   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x0800122d   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001311   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08001325   Thumb Code   128  strcmpv7m.o(.text)
    _sys_exit                                0x080013a5   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080013b1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080013b1   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080013b3   Thumb Code     0  indicate_semi.o(.text)
    Error_Handler                            0x080013b5   Thumb Code    10  main.o(.text.Error_Handler)
    HAL_Delay                                0x080013c1   Thumb Code    40  stm32f1xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x080013e9   Thumb Code   526  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080015f9   Thumb Code    10  stm32f1xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001605   Thumb Code    12  stm32f1xx_hal.o(.text.HAL_GetTick)
    HAL_I2C_Init                             0x08001611   Thumb Code   356  stm32f1xx_hal_i2c.o(.text.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x08001775   Thumb Code   138  i2c.o(.text.HAL_I2C_MspInit)
    HAL_Init                                 0x08001801   Thumb Code    38  stm32f1xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001829   Thumb Code    72  stm32f1xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001871   Thumb Code     2  stm32f1xx_hal.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08001875   Thumb Code    34  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001899   Thumb Code    86  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080018f1   Thumb Code    32  stm32f1xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001911   Thumb Code   442  stm32f1xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001acd   Thumb Code    38  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001af5   Thumb Code    38  stm32f1xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_OscConfig                        0x08001b1d   Thumb Code  1024  stm32f1xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001f1d   Thumb Code    44  stm32f1xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08001f49   Thumb Code   126  stm32f1xx_hal_tim_ex.o(.text.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08001fc9   Thumb Code   250  stm32f1xx_hal_tim.o(.text.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080020c5   Thumb Code    62  tim.o(.text.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08002105   Thumb Code   416  stm32f1xx_hal_tim.o(.text.HAL_TIM_ConfigClockSource)
    HAL_UART_Init                            0x080022a5   Thumb Code   226  stm32f1xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002389   Thumb Code   250  usart.o(.text.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08002485   Thumb Code   402  stm32f1xx_hal_uart.o(.text.HAL_UART_Transmit)
    MX_GPIO_Init                             0x08002619   Thumb Code   120  gpio.o(.text.MX_GPIO_Init)
    MX_I2C1_Init                             0x08002691   Thumb Code    66  i2c.o(.text.MX_I2C1_Init)
    MX_TIM2_Init                             0x080026d5   Thumb Code   112  tim.o(.text.MX_TIM2_Init)
    MX_USART1_UART_Init                      0x08002745   Thumb Code    60  usart.o(.text.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08002781   Thumb Code    60  usart.o(.text.MX_USART2_UART_Init)
    SystemInit                               0x080027bd   Thumb Code    80  system_stm32f1xx.o(.text.SystemInit)
    main                                     0x0800280d   Thumb Code   268  main.o(.text.main)
    my_printf                                0x080029a1   Thumb Code    62  usart.o(.text.my_printf)
    _btod_d2e                                0x080029df   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08002a1d   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002a63   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002ac5   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002dfd   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002ed9   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002f03   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08002f2d   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08003171   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08003199   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x080031a9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080031d5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _printf_fp_dec                           0x08003201   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08003205   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08003208   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08003304   Data          16  system_stm32f1xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08003314   Data           8  system_stm32f1xx.o(.rodata.APBPrescTable)
    Region$$Table$$Base                      0x080033d0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080033f0   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08003419   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f1xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x20000004   Data           4  stm32f1xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f1xx.o(.data.SystemCoreClock)
    __libspace_start                         0x20000010   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000070   Data           0  libspace.o(.bss)
    hi2c1                                    0x20000070   Data          84  i2c.o(.bss.hi2c1)
    htim2                                    0x200000c4   Data          72  tim.o(.bss.htim2)
    huart1                                   0x2000010c   Data          72  usart.o(.bss.huart1)
    huart2                                   0x20000154   Data          72  usart.o(.bss.huart2)
    uwTick                                   0x2000019c   Data           4  stm32f1xx_hal.o(.bss.uwTick)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003530, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000351c, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO            3    RESET               startup_stm32f103xb.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         1131  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x0000005c   Code   RO         1553    !!!scatter          c_w.l(__scatter.o)
    0x08000150   0x08000150   0x0000001a   Code   RO         1557    !!handler_copy      c_w.l(__scatter_copy.o)
    0x0800016a   0x0800016a   0x00000002   PAD
    0x0800016c   0x0800016c   0x00000002   Code   RO         1554    !!handler_null      c_w.l(__scatter.o)
    0x0800016e   0x0800016e   0x00000002   PAD
    0x08000170   0x08000170   0x0000001c   Code   RO         1559    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800018c   0x0800018c   0x00000000   Code   RO         1255    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800018c   0x0800018c   0x00000006   Code   RO         1244    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000192   0x08000192   0x00000006   Code   RO         1246    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000198   0x08000198   0x00000006   Code   RO         1251    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800019e   0x0800019e   0x00000006   Code   RO         1252    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO         1253    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x080001aa   0x080001aa   0x00000006   Code   RO         1254    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x080001b0   0x080001b0   0x0000000a   Code   RO         1259    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x080001ba   0x080001ba   0x00000006   Code   RO         1248    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x080001c0   0x080001c0   0x00000006   Code   RO         1249    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001c6   0x080001c6   0x00000006   Code   RO         1250    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001cc   0x080001cc   0x00000006   Code   RO         1247    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001d2   0x080001d2   0x00000006   Code   RO         1245    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO         1256    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001de   0x080001de   0x00000006   Code   RO         1257    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO         1258    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x080001ea   0x080001ea   0x00000006   Code   RO         1263    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080001f0   0x080001f0   0x00000006   Code   RO         1264    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080001f6   0x080001f6   0x0000000a   Code   RO         1260    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000200   0x08000200   0x00000006   Code   RO         1242    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000206   0x08000206   0x00000006   Code   RO         1243    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800020c   0x0800020c   0x00000006   Code   RO         1261    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000212   0x08000212   0x00000006   Code   RO         1262    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000218   0x08000218   0x00000004   Code   RO         1343    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800021c   0x0800021c   0x00000002   Code   RO         1401    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1418    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1420    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1422    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1425    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1427    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000000   Code   RO         1429    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO         1430    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x00000000   Code   RO         1432    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000224   0x08000224   0x0000000c   Code   RO         1433    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1434    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x00000000   Code   RO         1436    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000230   0x08000230   0x0000000a   Code   RO         1437    .ARM.Collect$$libinit$$00000018  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1438    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1440    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1442    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1444    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1446    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1448    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1450    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1452    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1456    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1458    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1460    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000000   Code   RO         1462    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x0800023a   0x0800023a   0x00000002   Code   RO         1463    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000002   Code   RO         1489    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1504    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1506    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1509    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1512    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1514    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000000   Code   RO         1517    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x0800023e   0x0800023e   0x00000002   Code   RO         1518    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1171    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000240   0x08000240   0x00000000   Code   RO         1307    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000240   0x08000240   0x00000006   Code   RO         1319    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000246   0x08000246   0x00000000   Code   RO         1309    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000246   0x08000246   0x00000004   Code   RO         1310    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000000   Code   RO         1312    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800024a   0x0800024a   0x00000008   Code   RO         1313    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000252   0x08000252   0x00000002   Code   RO         1408    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000254   0x08000254   0x00000000   Code   RO         1467    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000254   0x08000254   0x00000004   Code   RO         1468    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000258   0x08000258   0x00000006   Code   RO         1469    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800025e   0x0800025e   0x00000002   PAD
    0x08000260   0x08000260   0x00000040   Code   RO            4    .text               startup_stm32f103xb.o
    0x080002a0   0x080002a0   0x0000003c   Code   RO         1125    .text               c_w.l(vsnprintf.o)
    0x080002dc   0x080002dc   0x00000006   Code   RO         1129    .text               c_w.l(heapauxi.o)
    0x080002e2   0x080002e2   0x0000004e   Code   RO         1176    .text               c_w.l(_printf_pad.o)
    0x08000330   0x08000330   0x00000024   Code   RO         1178    .text               c_w.l(_printf_truncate.o)
    0x08000354   0x08000354   0x00000052   Code   RO         1180    .text               c_w.l(_printf_str.o)
    0x080003a6   0x080003a6   0x00000002   PAD
    0x080003a8   0x080003a8   0x00000078   Code   RO         1182    .text               c_w.l(_printf_dec.o)
    0x08000420   0x08000420   0x00000028   Code   RO         1184    .text               c_w.l(_printf_charcount.o)
    0x08000448   0x08000448   0x00000030   Code   RO         1186    .text               c_w.l(_printf_char_common.o)
    0x08000478   0x08000478   0x00000010   Code   RO         1188    .text               c_w.l(_snputc.o)
    0x08000488   0x08000488   0x000000bc   Code   RO         1190    .text               c_w.l(_printf_wctomb.o)
    0x08000544   0x08000544   0x0000007c   Code   RO         1193    .text               c_w.l(_printf_longlong_dec.o)
    0x080005c0   0x080005c0   0x00000070   Code   RO         1199    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000630   0x08000630   0x00000094   Code   RO         1219    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080006c4   0x080006c4   0x00000188   Code   RO         1239    .text               c_w.l(__printf_flags_ss_wp.o)
    0x0800084c   0x0800084c   0x0000008a   Code   RO         1326    .text               c_w.l(lludiv10.o)
    0x080008d6   0x080008d6   0x000000b2   Code   RO         1328    .text               c_w.l(_printf_intcommon.o)
    0x08000988   0x08000988   0x0000041c   Code   RO         1330    .text               c_w.l(_printf_fp_dec.o)
    0x08000da4   0x08000da4   0x000002fc   Code   RO         1334    .text               c_w.l(_printf_fp_hex.o)
    0x080010a0   0x080010a0   0x0000002c   Code   RO         1339    .text               c_w.l(_printf_char.o)
    0x080010cc   0x080010cc   0x0000002c   Code   RO         1341    .text               c_w.l(_printf_wchar.o)
    0x080010f8   0x080010f8   0x00000048   Code   RO         1344    .text               c_w.l(_c16rtomb.o)
    0x08001140   0x08001140   0x00000008   Code   RO         1350    .text               c_w.l(libspace.o)
    0x08001148   0x08001148   0x0000004a   Code   RO         1353    .text               c_w.l(sys_stackheap_outer.o)
    0x08001192   0x08001192   0x00000002   PAD
    0x08001194   0x08001194   0x00000010   Code   RO         1355    .text               c_w.l(rt_ctype_table.o)
    0x080011a4   0x080011a4   0x00000008   Code   RO         1360    .text               c_w.l(rt_locale_intlibspace.o)
    0x080011ac   0x080011ac   0x00000080   Code   RO         1362    .text               c_w.l(_printf_fp_infnan.o)
    0x0800122c   0x0800122c   0x000000e4   Code   RO         1364    .text               c_w.l(bigflt0.o)
    0x08001310   0x08001310   0x00000012   Code   RO         1394    .text               c_w.l(exit.o)
    0x08001322   0x08001322   0x00000002   PAD
    0x08001324   0x08001324   0x00000080   Code   RO         1415    .text               c_w.l(strcmpv7m.o)
    0x080013a4   0x080013a4   0x0000000c   Code   RO         1479    .text               c_w.l(sys_exit.o)
    0x080013b0   0x080013b0   0x00000002   Code   RO         1494    .text               c_w.l(use_no_semi.o)
    0x080013b2   0x080013b2   0x00000000   Code   RO         1496    .text               c_w.l(indicate_semi.o)
    0x080013b2   0x080013b2   0x00000002   PAD
    0x080013b4   0x080013b4   0x0000000a   Code   RO           30    .text.Error_Handler  main.o
    0x080013be   0x080013be   0x00000002   PAD
    0x080013c0   0x080013c0   0x00000028   Code   RO          284    .text.HAL_Delay     stm32f1xx_hal.o
    0x080013e8   0x080013e8   0x0000020e   Code   RO          376    .text.HAL_GPIO_Init  stm32f1xx_hal_gpio.o
    0x080015f6   0x080015f6   0x00000002   PAD
    0x080015f8   0x080015f8   0x0000000a   Code   RO          382    .text.HAL_GPIO_WritePin  stm32f1xx_hal_gpio.o
    0x08001602   0x08001602   0x00000002   PAD
    0x08001604   0x08001604   0x0000000c   Code   RO          276    .text.HAL_GetTick   stm32f1xx_hal.o
    0x08001610   0x08001610   0x00000164   Code   RO          126    .text.HAL_I2C_Init  stm32f1xx_hal_i2c.o
    0x08001774   0x08001774   0x0000008a   Code   RO           54    .text.HAL_I2C_MspInit  i2c.o
    0x080017fe   0x080017fe   0x00000002   PAD
    0x08001800   0x08001800   0x00000026   Code   RO          264    .text.HAL_Init      stm32f1xx_hal.o
    0x08001826   0x08001826   0x00000002   PAD
    0x08001828   0x08001828   0x00000048   Code   RO          266    .text.HAL_InitTick  stm32f1xx_hal.o
    0x08001870   0x08001870   0x00000002   Code   RO          268    .text.HAL_MspInit   stm32f1xx_hal.o
    0x08001872   0x08001872   0x00000002   PAD
    0x08001874   0x08001874   0x00000022   Code   RO          436    .text.HAL_NVIC_EnableIRQ  stm32f1xx_hal_cortex.o
    0x08001896   0x08001896   0x00000002   PAD
    0x08001898   0x08001898   0x00000056   Code   RO          434    .text.HAL_NVIC_SetPriority  stm32f1xx_hal_cortex.o
    0x080018ee   0x080018ee   0x00000002   PAD
    0x080018f0   0x080018f0   0x00000020   Code   RO          432    .text.HAL_NVIC_SetPriorityGrouping  stm32f1xx_hal_cortex.o
    0x08001910   0x08001910   0x000001ba   Code   RO          328    .text.HAL_RCC_ClockConfig  stm32f1xx_hal_rcc.o
    0x08001aca   0x08001aca   0x00000002   PAD
    0x08001acc   0x08001acc   0x00000026   Code   RO          340    .text.HAL_RCC_GetPCLK1Freq  stm32f1xx_hal_rcc.o
    0x08001af2   0x08001af2   0x00000002   PAD
    0x08001af4   0x08001af4   0x00000026   Code   RO          342    .text.HAL_RCC_GetPCLK2Freq  stm32f1xx_hal_rcc.o
    0x08001b1a   0x08001b1a   0x00000002   PAD
    0x08001b1c   0x08001b1c   0x00000400   Code   RO          326    .text.HAL_RCC_OscConfig  stm32f1xx_hal_rcc.o
    0x08001f1c   0x08001f1c   0x0000002c   Code   RO          444    .text.HAL_SYSTICK_Config  stm32f1xx_hal_cortex.o
    0x08001f48   0x08001f48   0x0000007e   Code   RO          892    .text.HAL_TIMEx_MasterConfigSynchronization  stm32f1xx_hal_tim_ex.o
    0x08001fc6   0x08001fc6   0x00000002   PAD
    0x08001fc8   0x08001fc8   0x000000fa   Code   RO          597    .text.HAL_TIM_Base_Init  stm32f1xx_hal_tim.o
    0x080020c2   0x080020c2   0x00000002   PAD
    0x080020c4   0x080020c4   0x0000003e   Code   RO           79    .text.HAL_TIM_Base_MspInit  tim.o
    0x08002102   0x08002102   0x00000002   PAD
    0x08002104   0x08002104   0x000001a0   Code   RO          777    .text.HAL_TIM_ConfigClockSource  stm32f1xx_hal_tim.o
    0x080022a4   0x080022a4   0x000000e2   Code   RO          916    .text.HAL_UART_Init  stm32f1xx_hal_uart.o
    0x08002386   0x08002386   0x00000002   PAD
    0x08002388   0x08002388   0x000000fa   Code   RO           96    .text.HAL_UART_MspInit  usart.o
    0x08002482   0x08002482   0x00000002   PAD
    0x08002484   0x08002484   0x00000192   Code   RO          930    .text.HAL_UART_Transmit  stm32f1xx_hal_uart.o
    0x08002616   0x08002616   0x00000002   PAD
    0x08002618   0x08002618   0x00000078   Code   RO           43    .text.MX_GPIO_Init  gpio.o
    0x08002690   0x08002690   0x00000042   Code   RO           52    .text.MX_I2C1_Init  i2c.o
    0x080026d2   0x080026d2   0x00000002   PAD
    0x080026d4   0x080026d4   0x00000070   Code   RO           77    .text.MX_TIM2_Init  tim.o
    0x08002744   0x08002744   0x0000003c   Code   RO           92    .text.MX_USART1_UART_Init  usart.o
    0x08002780   0x08002780   0x0000003c   Code   RO           94    .text.MX_USART2_UART_Init  usart.o
    0x080027bc   0x080027bc   0x00000050   Code   RO           11    .text.SystemInit    system_stm32f1xx.o
    0x0800280c   0x0800280c   0x00000194   Code   RO           26    .text.main          main.o
    0x080029a0   0x080029a0   0x0000003e   Code   RO          100    .text.my_printf     usart.o
    0x080029de   0x080029de   0x0000003e   Code   RO         1367    CL$$btod_d2e        c_w.l(btod.o)
    0x08002a1c   0x08002a1c   0x00000046   Code   RO         1369    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002a62   0x08002a62   0x00000060   Code   RO         1368    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002ac2   0x08002ac2   0x00000002   PAD
    0x08002ac4   0x08002ac4   0x00000338   Code   RO         1377    CL$$btod_div_common  c_w.l(btod.o)
    0x08002dfc   0x08002dfc   0x000000dc   Code   RO         1374    CL$$btod_e2e        c_w.l(btod.o)
    0x08002ed8   0x08002ed8   0x0000002a   Code   RO         1371    CL$$btod_ediv       c_w.l(btod.o)
    0x08002f02   0x08002f02   0x0000002a   Code   RO         1370    CL$$btod_emul       c_w.l(btod.o)
    0x08002f2c   0x08002f2c   0x00000244   Code   RO         1376    CL$$btod_mult_common  c_w.l(btod.o)
    0x08003170   0x08003170   0x00000028   Code   RO         1406    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08003198   0x08003198   0x0000000e   Code   RO         1232    i._is_digit         c_w.l(__printf_wp.o)
    0x080031a6   0x080031a6   0x00000002   PAD
    0x080031a8   0x080031a8   0x0000002c   Code   RO         1392    locale$$code        c_w.l(lc_numeric_c.o)
    0x080031d4   0x080031d4   0x0000002c   Code   RO         1413    locale$$code        c_w.l(lc_ctype_c.o)
    0x08003200   0x08003200   0x00000004   Code   RO         1279    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08003204   0x08003204   0x00000004   Code   RO         1281    x$fpl$printf2       fz_ws.l(printf2.o)
    0x08003208   0x08003208   0x00000000   Code   RO         1289    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003208   0x08003208   0x00000008   Data   RO         1191    .constdata          c_w.l(_printf_wctomb.o)
    0x08003210   0x08003210   0x00000028   Data   RO         1220    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08003238   0x08003238   0x00000011   Data   RO         1240    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08003249   0x08003249   0x00000026   Data   RO         1335    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800326f   0x0800326f   0x00000001   PAD
    0x08003270   0x08003270   0x00000094   Data   RO         1365    .constdata          c_w.l(bigflt0.o)
    0x08003304   0x08003304   0x00000010   Data   RO           16    .rodata.AHBPrescTable  system_stm32f1xx.o
    0x08003314   0x08003314   0x00000008   Data   RO           17    .rodata.APBPrescTable  system_stm32f1xx.o
    0x0800331c   0x0800331c   0x00000002   Data   RO          353    .rodata.HAL_RCC_GetSysClockFreq.aPredivFactorTable  stm32f1xx_hal_rcc.o
    0x0800331e   0x0800331e   0x00000010   Data   RO          352    .rodata.cst16       stm32f1xx_hal_rcc.o
    0x0800332e   0x0800332e   0x0000009f   Data   RO           32    .rodata.str1.1      main.o
    0x080033cd   0x080033cd   0x00000003   PAD
    0x080033d0   0x080033d0   0x00000020   Data   RO         1552    Region$$Table       anon$$obj.o
    0x080033f0   0x080033f0   0x0000001c   Data   RO         1391    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800340c   0x0800340c   0x00000110   Data   RO         1412    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08003520, Size: 0x000007a0, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08003520   0x00000008   Data   RW          315    .data..L_MergedGlobals  stm32f1xx_hal.o
    0x20000008   0x08003528   0x00000004   Data   RW           15    .data.SystemCoreClock  system_stm32f1xx.o
    0x2000000c   0x0800352c   0x00000004   PAD
    0x20000010        -       0x00000060   Zero   RW         1351    .bss                c_w.l(libspace.o)
    0x20000070        -       0x00000054   Zero   RW           68    .bss.hi2c1          i2c.o
    0x200000c4        -       0x00000048   Zero   RW           83    .bss.htim2          tim.o
    0x2000010c        -       0x00000048   Zero   RW          102    .bss.huart1         usart.o
    0x20000154        -       0x00000048   Zero   RW          103    .bss.huart2         usart.o
    0x2000019c        -       0x00000004   Zero   RW          314    .bss.uwTick         stm32f1xx_hal.o
    0x200001a0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f103xb.o
    0x200003a0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f103xb.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       120          0          0          0          0       1552   gpio.o
       204          0          0          0         84       8140   i2c.o
       414        136        159          0          0       3745   main.o
        64         26        236          0       1536        804   startup_stm32f103xb.o
       164          0          0          8          4       5766   stm32f1xx_hal.o
       196          0          0          0          0       8420   stm32f1xx_hal_cortex.o
       536         22          0          0          0       4694   stm32f1xx_hal_gpio.o
       356          0          0          0          0      44992   stm32f1xx_hal_i2c.o
      1542          0         18          0          0       7954   stm32f1xx_hal_rcc.o
       666          0          0          0          0      57673   stm32f1xx_hal_tim.o
       126          0          0          0          0      20915   stm32f1xx_hal_tim_ex.o
       628          0          0          0          0      32626   stm32f1xx_hal_uart.o
        80          0         24          4          0       2146   system_stm32f1xx.o
       174          0          0          0         72       5926   tim.o
       432          0          0          0        144       6425   usart.o

    ----------------------------------------------------------------------
      5738        <USER>        <GROUP>         12       1844     211778   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        36          0          3          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        72          0          0          0          0         96   _c16rtomb.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1052          0          0          0          0        148   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112          8          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        16          0          0          0          0         68   _snputc.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        668   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        60          4          0          0          0         80   vsnprintf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      6834        <USER>        <GROUP>          0         96       4032   Library Totals
        18          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6768        264        551          0         96       3828   c_w.l
         8          0          0          0          0        136   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      6834        <USER>        <GROUP>          0         96       4032   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12572        448       1024         12       1940     213482   Grand Totals
     12572        448       1024         12       1940     213482   ELF Image Totals
     12572        448       1024         12          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13596 (  13.28kB)
    Total RW  Size (RW Data + ZI Data)              1952 (   1.91kB)
    Total ROM Size (Code + RO Data + RW Data)      13608 (  13.29kB)

==============================================================================

