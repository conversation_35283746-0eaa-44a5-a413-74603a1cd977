/**
  ******************************************************************************
  * @file    bno080_test.c
  * @brief   This file provides code for BNO080 unit tests and validation
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bno080_test.h"
#include "usart.h"
#include <math.h>
#include <string.h>

/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

// 全局测试结果
static BNO080_TestResults_t g_test_results = {0};

/* USER CODE BEGIN 1 */

/**
 * @brief  运行所有测试
 */
void BNO080_RunAllTests(void)
{
    uint32_t start_time = HAL_GetTick();
    
    my_printf(&huart2, "\r\n=== BNO080单元测试开始 ===\r\n");
    
    // 清除测试结果
    BNO080_ClearTestResults();
    
    // 运行各项测试
    BNO080_TestI2CCommunication();
    BNO080_TestDataConversion();
    BNO080_TestErrorHandling();
    BNO080_TestPerformance();
    
    // 计算测试持续时间
    g_test_results.test_duration_ms = HAL_GetTick() - start_time;
    
    // 输出测试结果
    my_printf(&huart2, "\r\n=== 测试结果汇总 ===\r\n");
    my_printf(&huart2, "总测试数: %lu\r\n", g_test_results.total_tests);
    my_printf(&huart2, "通过测试: %lu\r\n", g_test_results.passed_tests);
    my_printf(&huart2, "失败测试: %lu\r\n", g_test_results.failed_tests);
    my_printf(&huart2, "测试时间: %lu ms\r\n", g_test_results.test_duration_ms);
    
    if (g_test_results.failed_tests == 0)
    {
        my_printf(&huart2, "✓ 所有测试通过!\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 有 %lu 个测试失败\r\n", g_test_results.failed_tests);
    }
    
    my_printf(&huart2, "=== BNO080单元测试结束 ===\r\n\r\n");
}

/**
 * @brief  测试I2C通信功能
 */
void BNO080_TestI2CCommunication(void)
{
    my_printf(&huart2, "\r\n--- I2C通信测试 ---\r\n");
    
    // 测试1: 设备检测
    g_test_results.total_tests++;
    if (BNO080_I2C_IsReady() == HAL_OK)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 设备检测测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 设备检测测试失败\r\n");
    }
    
    // 测试2: 设备复位
    g_test_results.total_tests++;
    if (BNO080_I2C_Reset() == HAL_OK)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 设备复位测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 设备复位测试失败\r\n");
    }
    
    // 等待复位完成
    HAL_Delay(200);
    
    // 测试3: 状态检查
    g_test_results.total_tests++;
    if (BNO080_I2C_CheckStatus() == HAL_OK)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 状态检查测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 状态检查测试失败\r\n");
    }
    
    // 测试4: 产品ID读取
    g_test_results.total_tests++;
    uint8_t product_id[20] = {0};
    uint16_t id_length = 0;
    if (BNO080_I2C_ReadProductID(product_id, &id_length) == HAL_OK && id_length > 0)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 产品ID读取测试通过 (长度: %d)\r\n", id_length);
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 产品ID读取测试失败\r\n");
    }
    
    // 测试5: 传感器配置
    g_test_results.total_tests++;
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 传感器配置测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 传感器配置测试失败\r\n");
    }
}

/**
 * @brief  测试数据转换功能
 */
void BNO080_TestDataConversion(void)
{
    my_printf(&huart2, "\r\n--- 数据转换测试 ---\r\n");
    
    // 测试1: 单位四元数 (无旋转)
    g_test_results.total_tests++;
    if (BNO080_TestQuaternionToEuler(1.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 0.0f))
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 单位四元数测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 单位四元数测试失败\r\n");
    }
    
    // 测试2: 90度偏航旋转
    g_test_results.total_tests++;
    float sqrt2_2 = 0.7071067811865476f;  // sqrt(2)/2
    if (BNO080_TestQuaternionToEuler(sqrt2_2, 0.0f, 0.0f, sqrt2_2, 90.0f, 0.0f, 0.0f))
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 90度偏航旋转测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 90度偏航旋转测试失败\r\n");
    }
    
    // 测试3: 90度俯仰旋转
    g_test_results.total_tests++;
    if (BNO080_TestQuaternionToEuler(sqrt2_2, 0.0f, sqrt2_2, 0.0f, 0.0f, 90.0f, 0.0f))
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 90度俯仰旋转测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 90度俯仰旋转测试失败\r\n");
    }
    
    // 测试4: 90度横滚旋转
    g_test_results.total_tests++;
    if (BNO080_TestQuaternionToEuler(sqrt2_2, sqrt2_2, 0.0f, 0.0f, 0.0f, 0.0f, 90.0f))
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 90度横滚旋转测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 90度横滚旋转测试失败\r\n");
    }
}

/**
 * @brief  测试错误处理功能
 */
void BNO080_TestErrorHandling(void)
{
    my_printf(&huart2, "\r\n--- 错误处理测试 ---\r\n");
    
    // 清除错误统计
    BNO080_ClearErrorStats();
    
    // 测试1: 错误统计功能
    g_test_results.total_tests++;
    BNO080_ErrorStats_t stats_before, stats_after;
    BNO080_GetErrorStats(&stats_before);
    
    // 触发一个错误
    BNO080_ErrorHandler(BNO080_ERROR_I2C_TIMEOUT);
    
    BNO080_GetErrorStats(&stats_after);
    
    if (stats_after.total_error_count == stats_before.total_error_count + 1 &&
        stats_after.i2c_timeout_count == stats_before.i2c_timeout_count + 1)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 错误统计测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 错误统计测试失败\r\n");
    }
    
    // 测试2: 设备在线状态
    g_test_results.total_tests++;
    uint8_t online_status = BNO080_IsDeviceOnline();
    if (online_status == 1 || online_status == 0)  // 只要返回有效值就算通过
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 设备在线状态测试通过 (状态: %s)\r\n", 
                 online_status ? "在线" : "离线");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 设备在线状态测试失败\r\n");
    }
    
    // 测试3: 错误清除功能
    g_test_results.total_tests++;
    BNO080_ClearErrorStats();
    BNO080_GetErrorStats(&stats_after);
    
    if (stats_after.total_error_count == 0)
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 错误清除测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 错误清除测试失败\r\n");
    }
}

/**
 * @brief  测试性能功能
 */
void BNO080_TestPerformance(void)
{
    my_printf(&huart2, "\r\n--- 性能测试 ---\r\n");
    
    // 测试1: 数据读取性能
    g_test_results.total_tests++;
    uint32_t start_time = HAL_GetTick();
    uint32_t read_count = 0;
    uint32_t success_count = 0;
    
    // 进行100次读取测试
    for (int i = 0; i < 100; i++)
    {
        read_count++;
        uint8_t test_buffer[32] = {0};
        if (BNO080_I2C_ReadSensorData(test_buffer, sizeof(test_buffer)) == HAL_OK)
        {
            success_count++;
        }
        HAL_Delay(1);  // 1ms间隔
    }
    
    uint32_t total_time = HAL_GetTick() - start_time;
    float success_rate = (float)success_count / read_count * 100.0f;
    
    if (success_rate >= 50.0f)  // 至少50%成功率
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 数据读取性能测试通过\r\n");
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 数据读取性能测试失败\r\n");
    }
    
    my_printf(&huart2, "  读取次数: %lu, 成功次数: %lu\r\n", read_count, success_count);
    my_printf(&huart2, "  成功率: %.1f%%, 总时间: %lu ms\r\n", success_rate, total_time);
    
    // 测试2: 内存使用测试
    g_test_results.total_tests++;
    BNO080_Data_t test_data;
    BNO080_GetData(&test_data);
    
    // 简单检查数据结构是否正常
    if (sizeof(test_data) == sizeof(BNO080_Data_t))
    {
        g_test_results.passed_tests++;
        my_printf(&huart2, "✓ 内存使用测试通过 (数据结构大小: %d 字节)\r\n", 
                 sizeof(BNO080_Data_t));
    }
    else
    {
        g_test_results.failed_tests++;
        my_printf(&huart2, "✗ 内存使用测试失败\r\n");
    }
}

/**
 * @brief  获取测试结果
 * @param  results: 测试结果结构体指针
 */
void BNO080_GetTestResults(BNO080_TestResults_t* results)
{
    if (results != NULL)
    {
        memcpy(results, &g_test_results, sizeof(BNO080_TestResults_t));
    }
}

/**
 * @brief  清除测试结果
 */
void BNO080_ClearTestResults(void)
{
    memset(&g_test_results, 0, sizeof(BNO080_TestResults_t));
}

/**
 * @brief  测试四元数到欧拉角转换
 * @param  qw, qx, qy, qz: 四元数分量
 * @param  expected_yaw, expected_pitch, expected_roll: 期望的欧拉角
 * @retval 1: 测试通过, 0: 测试失败
 */
uint8_t BNO080_TestQuaternionToEuler(float qw, float qx, float qy, float qz, 
                                     float expected_yaw, float expected_pitch, float expected_roll)
{
    // 四元数归一化
    float norm = sqrtf(qw*qw + qx*qx + qy*qy + qz*qz);
    if (norm > 1e-6f)
    {
        qw /= norm; qx /= norm; qy /= norm; qz /= norm;
    }
    
    // 四元数转欧拉角 (与BNO080_I2C_ProcessData中的算法一致)
    const float rad_to_deg = 180.0f / 3.14159265358979323846f;
    
    float qx_qy = qx * qy;
    float qw_qz = qw * qz;
    float qx_qx = qx * qx;
    float qy_qy = qy * qy;
    float qz_qz = qz * qz;
    float qw_qx = qw * qx;
    float qy_qz = qy * qz;
    float qw_qy = qw * qy;
    float qz_qx = qz * qx;
    
    float yaw = atan2f(2.0f * (qw_qz + qx_qy), 1.0f - 2.0f * (qy_qy + qz_qz)) * rad_to_deg;
    float pitch = asinf(2.0f * (qw_qy - qz_qx)) * rad_to_deg;
    float roll = atan2f(2.0f * (qw_qx + qy_qz), 1.0f - 2.0f * (qx_qx + qy_qy)) * rad_to_deg;
    
    // 检查结果是否在容差范围内
    const float tolerance = 1.0f;  // 1度容差
    
    return (BNO080_TestFloatEqual(yaw, expected_yaw, tolerance) &&
            BNO080_TestFloatEqual(pitch, expected_pitch, tolerance) &&
            BNO080_TestFloatEqual(roll, expected_roll, tolerance));
}

/**
 * @brief  测试浮点数是否相等 (在容差范围内)
 * @param  a, b: 要比较的浮点数
 * @param  tolerance: 容差
 * @retval 1: 相等, 0: 不相等
 */
uint8_t BNO080_TestFloatEqual(float a, float b, float tolerance)
{
    return (fabsf(a - b) <= tolerance);
}

/* USER CODE END 1 */