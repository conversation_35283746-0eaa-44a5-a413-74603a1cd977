/**
  ******************************************************************************
  * @file    bno080_test.h
  * @brief   This file contains all the function prototypes for
  *          the bno080_test.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __BNO080_TEST_H__
#define __BNO080_TEST_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "bno080_hal.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

// 测试结果结构体
typedef struct {
    uint32_t total_tests;
    uint32_t passed_tests;
    uint32_t failed_tests;
    uint32_t test_duration_ms;
} BNO080_TestResults_t;

// 测试函数声明
void BNO080_RunAllTests(void);
void BNO080_TestI2CCommunication(void);
void BNO080_TestDataConversion(void);
void BNO080_TestErrorHandling(void);
void BNO080_TestPerformance(void);
void BNO080_GetTestResults(BNO080_TestResults_t* results);
void BNO080_ClearTestResults(void);

// 辅助测试函数
uint8_t BNO080_TestQuaternionToEuler(float qw, float qx, float qy, float qz, 
                                     float expected_yaw, float expected_pitch, float expected_roll);
uint8_t BNO080_TestFloatEqual(float a, float b, float tolerance);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ BNO080_TEST_H__ */