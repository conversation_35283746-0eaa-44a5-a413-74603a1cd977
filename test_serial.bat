@echo off
echo ========================================
echo BNO080 串口输出测试脚本
echo ========================================
echo.
echo 请按照以下步骤进行测试：
echo.
echo 1. 确保STM32已连接到电脑
echo 2. 打开串口调试工具（如PuTTY、SecureCRT等）
echo 3. 配置串口参数：
echo    - 波特率: 115200
echo    - 数据位: 8
echo    - 停止位: 1
echo    - 校验位: None
echo    - 流控制: None
echo.
echo 4. 连接到正确的COM端口
echo 5. 复位STM32或重新上电
echo.
echo 预期输出：
echo === BNO080 I2C Example Started ===
echo Step 1: Serial communication test
echo Step 2: System tick = [数字] ms
echo Step 3: I2C1 instance = [地址]
echo Step 4: Testing delay function...
echo Step 5: Delay test completed
echo Step 6: Skipping BNO080 initialization for now
echo === Initialization completed, entering main loop ===
echo [1] Serial test message - Time: [数字] ms
echo [2] Serial test message - Time: [数字] ms
echo ...
echo.
echo 如果只看到部分输出或乱码，请检查：
echo - 串口连接是否正确
echo - 波特率设置是否为115200
echo - 是否选择了正确的COM端口
echo - STM32是否正常运行
echo.
pause