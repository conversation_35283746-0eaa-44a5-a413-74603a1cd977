/**
  ******************************************************************************
  * @file    bno080_integration_test.h
  * @brief   This file contains all the function prototypes for
  *          the bno080_integration_test.c file
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __BNO080_INTEGRATION_TEST_H__
#define __BNO080_INTEGRATION_TEST_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "bno080_hal.h"

/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

// 集成测试结果结构体
typedef struct {
    uint32_t compatibility_tests;
    uint32_t compatibility_passed;
    uint32_t stability_tests;
    uint32_t stability_passed;
    uint32_t performance_tests;
    uint32_t performance_passed;
    uint32_t total_test_time_ms;
} BNO080_IntegrationTestResults_t;

// 集成测试函数声明
void BNO080_RunIntegrationTests(void);
void BNO080_TestApplicationCompatibility(void);
void BNO080_TestSystemStability(void);
void BNO080_TestDataAccuracy(void);
void BNO080_TestUpdateFrequency(void);
void BNO080_GetIntegrationTestResults(BNO080_IntegrationTestResults_t* results);

/* USER CODE BEGIN Prototypes */

/* USER CODE END Prototypes */

#ifdef __cplusplus
}
#endif
#endif /*__ BNO080_INTEGRATION_TEST_H__ */