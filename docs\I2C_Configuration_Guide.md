# BNO080 I2C配置和使用指南

## 概述

本文档详细介绍了BNO080传感器的I2C配置方法和使用说明，包括硬件连接、软件配置、通信协议和常见问题解决方案。

## 硬件配置

### BNO080协议选择

BNO080支持多种通信协议，通过PS0和PS1引脚进行选择：

| PS0 | PS1 | 通信协议 |
|-----|-----|----------|
| 0   | 0   | I2C      |
| 1   | 0   | UART-RVC |
| 0   | 1   | UART-HID |
| 1   | 1   | SPI      |

**本项目使用I2C模式：PS0=0, PS1=0**

### I2C地址配置

BNO080的I2C地址由ADR引脚决定：
- ADR引脚接GND：I2C地址 = 0x4A
- ADR引脚接VCC：I2C地址 = 0x4B

### 硬件连接图

```
STM32F103C8T6          BNO080
┌─────────────┐       ┌─────────────┐
│     PB6     │──────▶│     SCL     │
│     PB7     │◀─────▶│     SDA     │
│     PB0     │──────▶│     PS0     │
│     PB1     │──────▶│     PS1     │
│     3.3V    │──────▶│     VCC     │
│     GND     │──────▶│     GND     │
└─────────────┘       └─────────────┘
```

### 上拉电阻

I2C总线需要上拉电阻：
- SCL和SDA线路各需要一个4.7kΩ上拉电阻连接到3.3V
- 如果使用开发板，可能已经集成了上拉电阻

## 软件配置

### STM32CubeMX配置

1. **I2C1配置**
   - Mode: I2C
   - I2C Speed: Fast Mode (400kHz)
   - Clock No Stretch Mode: Disable
   - General Call Address Detection: Disable

2. **GPIO配置**
   - PB6: I2C1_SCL (Alternate Function Open Drain)
   - PB7: I2C1_SDA (Alternate Function Open Drain)
   - PB0: GPIO_Output (PS0控制)
   - PB1: GPIO_Output (PS1控制)

3. **定时器配置**
   - TIM2: 100Hz中断频率
   - Prescaler: 72-1 (1MHz)
   - Period: 10000-1 (100Hz)

### 代码配置

#### I2C初始化
```c
void MX_I2C1_Init(void)
{
    hi2c1.Instance = I2C1;
    hi2c1.Init.ClockSpeed = 400000;        // 400kHz
    hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
    hi2c1.Init.OwnAddress1 = 0;
    hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
    hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
    hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
    hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
    
    if (HAL_I2C_Init(&hi2c1) != HAL_OK) {
        Error_Handler();
    }
}
```

#### BNO080初始化序列
```c
void BNO080_Init(void)
{
    // 1. 配置GPIO引脚
    BNO080_GPIO_Config();
    
    // 2. 检测设备
    if (BNO080_I2C_IsReady() != HAL_OK) {
        // 设备检测失败处理
        return;
    }
    
    // 3. 软复位
    BNO080_I2C_Reset();
    HAL_Delay(100);
    
    // 4. 检查状态
    BNO080_I2C_CheckStatus();
    
    // 5. 配置传感器
    BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS);
}
```

## BNO080 I2C通信协议

### HID over I2C协议

BNO080使用HID over I2C协议进行通信，主要寄存器：

| 寄存器地址 | 名称 | 功能 |
|------------|------|------|
| 0x01 | HID_DESC | HID描述符 |
| 0x02 | REPORT_DESC | 报告描述符 |
| 0x03 | INPUT_REPORT | 输入报告（传感器数据） |
| 0x04 | OUTPUT_REPORT | 输出报告（命令） |
| 0x05 | COMMAND | 命令寄存器 |

### 数据读取流程

1. **发送配置命令**
   ```c
   uint8_t command[17] = {0};
   command[0] = BNO080_CMD_SET_FEATURE;
   command[1] = BNO080_SENSOR_ROTATION_VECTOR;
   // 设置报告间隔...
   I2C_Mem_Write(address, BNO080_REG_COMMAND, 1, command, 17, 100);
   ```

2. **读取传感器数据**
   ```c
   uint8_t data[14];
   I2C_Mem_Read(address, BNO080_REG_INPUT_REPORT, 1, data, 14, 100);
   ```

3. **解析数据包**
   ```c
   BNO080_RotationVector_t* rv = (BNO080_RotationVector_t*)data;
   if (rv->report_id == 0x05) {
       // 处理旋转向量数据
   }
   ```

### 数据包格式

#### 旋转向量报告 (Report ID: 0x05)
```c
typedef struct __packed {
    uint8_t report_id;          // 0x05
    uint8_t sequence_number;    // 序列号
    uint8_t status;            // 状态
    uint16_t delay;            // 延迟
    int16_t i_component;       // 四元数i (Q14格式)
    int16_t j_component;       // 四元数j (Q14格式)
    int16_t k_component;       // 四元数k (Q14格式)
    int16_t real_component;    // 四元数实部 (Q14格式)
    uint16_t accuracy;         // 精度估计
} BNO080_RotationVector_t;
```

## 数据处理

### 四元数到欧拉角转换

```c
void BNO080_QuaternionToEuler(float qw, float qx, float qy, float qz,
                              float* yaw, float* pitch, float* roll)
{
    // 归一化
    float norm = sqrtf(qw*qw + qx*qx + qy*qy + qz*qz);
    qw /= norm; qx /= norm; qy /= norm; qz /= norm;
    
    // 转换为欧拉角
    *yaw = atan2f(2.0f*(qw*qz + qx*qy), 1.0f - 2.0f*(qy*qy + qz*qz)) * 180.0f/PI;
    *pitch = asinf(2.0f*(qw*qy - qz*qx)) * 180.0f/PI;
    *roll = atan2f(2.0f*(qw*qx + qy*qz), 1.0f - 2.0f*(qx*qx + qy*qy)) * 180.0f/PI;
}
```

### Q14格式转换

BNO080输出的四元数采用Q14定点格式：
```c
float qw = (float)raw_qw / 16384.0f;  // 16384 = 2^14
```

## 性能优化

### I2C通信优化

1. **使用400kHz快速模式**
   ```c
   hi2c1.Init.ClockSpeed = 400000;
   ```

2. **非阻塞读取**
   ```c
   HAL_I2C_Mem_Read_IT(&hi2c1, address, reg, 1, buffer, size);
   ```

3. **中断优先级设置**
   ```c
   HAL_NVIC_SetPriority(I2C1_EV_IRQn, 2, 0);
   HAL_NVIC_SetPriority(I2C1_ER_IRQn, 2, 0);
   ```

### 数据处理优化

1. **预计算常用值**
   ```c
   const float q14_scale = 1.0f / 16384.0f;
   const float rad_to_deg = 180.0f / PI;
   ```

2. **避免重复计算**
   ```c
   float qx_qy = qx * qy;
   float qw_qz = qw * qz;
   // 使用预计算的值...
   ```

## 错误处理

### 常见错误类型

1. **I2C_TIMEOUT** - I2C通信超时
2. **I2C_NACK** - 设备无应答
3. **DEVICE_NOT_FOUND** - 设备未找到
4. **INVALID_DATA** - 数据无效
5. **CONFIG_FAIL** - 配置失败

### 错误处理策略

```c
void BNO080_ErrorHandler(BNO080_Error_t error)
{
    switch (error) {
        case BNO080_ERROR_I2C_TIMEOUT:
            // 重试通信
            break;
        case BNO080_ERROR_DEVICE_NOT_FOUND:
            // 标记设备离线
            break;
        // 其他错误处理...
    }
    
    // 每5次错误尝试恢复
    if (error_count % 5 == 0) {
        BNO080_RecoveryAttempt();
    }
}
```

## 调试技巧

### 1. 使用示波器检查I2C信号
- 检查SCL和SDA波形
- 验证时钟频率
- 确认ACK/NACK信号

### 2. 串口调试输出
```c
BNO080_SetDebugLevel(BNO080_DEBUG_VERBOSE);
BNO080_PrintDiagnostics();
```

### 3. 测试模式
```c
BNO080_TestMode();  // 运行完整的硬件测试
```

## 常见问题解答

### Q: 为什么选择I2C而不是UART？
A: I2C的优势：
- 节省引脚（只需2个引脚）
- 支持多设备连接
- 内置错误检测机制
- 更好的抗干扰能力

### Q: 如何提高I2C通信可靠性？
A: 建议措施：
- 使用合适的上拉电阻（4.7kΩ）
- 保持线路尽可能短
- 添加滤波电容
- 实现重试机制

### Q: 数据更新频率如何调整？
A: 修改报告间隔：
```c
// 50Hz: BNO080_REPORT_INTERVAL_20MS
// 100Hz: BNO080_REPORT_INTERVAL_10MS
BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS);
```

### Q: 如何处理传感器校准？
A: BNO080具有自动校准功能：
- 开机后需要一定时间进行校准
- 可以通过accuracy字段监控校准状态
- 避免在校准期间进行剧烈运动

## 参考资料

1. BNO080 Datasheet
2. STM32F103 Reference Manual
3. I2C Specification
4. HID over I2C Specification