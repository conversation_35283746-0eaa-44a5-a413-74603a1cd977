/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    usart.c
  * @brief   This file provides code for the configuration
  *          of the USART instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "usart.h"

/* USER CODE BEGIN 0 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>
#include <stdint.h>
#include <stdbool.h>

/* USER CODE END 0 */

UART_HandleTypeDef huart1;
UART_HandleTypeDef huart2;

/* USART1 init function */

void MX_USART1_UART_Init(void)
{

  /* USER CODE BEGIN USART1_Init 0 */

  /* USER CODE END USART1_Init 0 */

  /* USER CODE BEGIN USART1_Init 1 */

  /* USER CODE END USART1_Init 1 */
  huart1.Instance = USART1;
  huart1.Init.BaudRate = 115200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART1_Init 2 */

  /* USER CODE END USART1_Init 2 */

}
/* USART2 init function */

void MX_USART2_UART_Init(void)
{

  /* USER CODE BEGIN USART2_Init 0 */

  /* USER CODE END USART2_Init 0 */

  /* USER CODE BEGIN USART2_Init 1 */

  /* USER CODE END USART2_Init 1 */
  huart2.Instance = USART2;
  huart2.Init.BaudRate = 115200;
  huart2.Init.WordLength = UART_WORDLENGTH_8B;
  huart2.Init.StopBits = UART_STOPBITS_1;
  huart2.Init.Parity = UART_PARITY_NONE;
  huart2.Init.Mode = UART_MODE_TX_RX;
  huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart2.Init.OverSampling = UART_OVERSAMPLING_16;
  if (HAL_UART_Init(&huart2) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN USART2_Init 2 */

  /* USER CODE END USART2_Init 2 */

}

void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspInit 0 */

  /* USER CODE END USART1_MspInit 0 */
    /* USART1 clock enable */
    __HAL_RCC_USART1_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART1 GPIO Configuration
    PA9     ------> USART1_TX
    PA10     ------> USART1_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_9;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_10;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    /* USART1 interrupt Init */
    HAL_NVIC_SetPriority(USART1_IRQn, 0, 0);
    HAL_NVIC_EnableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspInit 1 */

  /* USER CODE END USART1_MspInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspInit 0 */

  /* USER CODE END USART2_MspInit 0 */
    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_2;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

    GPIO_InitStruct.Pin = GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /* USER CODE BEGIN USART2_MspInit 1 */

  /* USER CODE END USART2_MspInit 1 */
  }
}

void HAL_UART_MspDeInit(UART_HandleTypeDef* uartHandle)
{

  if(uartHandle->Instance==USART1)
  {
  /* USER CODE BEGIN USART1_MspDeInit 0 */

  /* USER CODE END USART1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART1_CLK_DISABLE();

    /**USART1 GPIO Configuration
    PA9     ------> USART1_TX
    PA10     ------> USART1_RX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_9|GPIO_PIN_10);

    /* USART1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(USART1_IRQn);
  /* USER CODE BEGIN USART1_MspDeInit 1 */

  /* USER CODE END USART1_MspDeInit 1 */
  }
  else if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspDeInit 0 */

  /* USER CODE END USART2_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_USART2_CLK_DISABLE();

    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    HAL_GPIO_DeInit(GPIOA, GPIO_PIN_2|GPIO_PIN_3);

  /* USER CODE BEGIN USART2_MspDeInit 1 */

  /* USER CODE END USART2_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */

// 实锟街讹拷锟斤拷锟斤拷诠锟斤拷锟揭伙拷锟斤拷锟接★拷锟斤拷锟�
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512]; // 锟斤拷时锟芥储锟斤拷式锟斤拷锟斤拷锟斤拷址锟斤拷锟�
	va_list arg;      // 锟斤拷锟斤拷杀锟斤拷锟斤拷
	int len;          // 锟斤拷锟斤拷锟街凤拷锟斤拷锟斤拷锟斤拷

	va_start(arg, format);
	// 锟斤拷全锟截革拷式锟斤拷锟街凤拷锟斤拷锟斤拷 buffer
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);

	// 通锟斤拷 HAL 锟解发锟斤拷 buffer 锟叫碉拷锟斤拷锟斤拷
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

/* USER CODE END 1 */
    HAL_Delay(50);
    
    // 从输入报告寄存器读取响应
    status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                         BNO080_REG_INPUT_REPORT, 
                         1, 
                         response, 
                         sizeof(response), 
                         100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "读取产品ID响应失败\r\n");
        return status;
    }
    
    // 检查响应是否为产品ID报告
    if (response[0] != BNO080_REPORT_PRODUCT_ID)
    {
        my_printf(&huart2, "收到的不是产品ID报告\r\n");
        return HAL_ERROR;
    }
    
    // 复制产品ID数据
    resp_len = response[1];  // 第二个字节包含数据长度
    if (resp_len > 0 && resp_len <= 18)  // 确保长度合理
    {
        memcpy(product_id, &response[2], resp_len);
        *length = resp_len;
        
        my_printf(&huart2, "成功读取产品ID\r\n");
        return HAL_OK;
    }
    
    my_printf(&huart2, "产品ID数据长度无效\r\n");
    return HAL_ERROR;
}

/**
 * @brief  检查BNO080状态
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_CheckStatus(void)
{
    uint8_t status_reg = 0;
    
    // 读取状态寄存器
    HAL_StatusTypeDef status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                                           0x00,  // 状态寄存器地址
                                           1, 
                                           &status_reg, 
                                           1, 
                                           100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "读取BNO080状态失败\r\n");
        return status;
    }
    
    // 检查状态寄存器的值
    if ((status_reg & 0x01) == 0)  // 检查就绪位
    {
        my_printf(&huart2, "BNO080未就绪\r\n");
        return HAL_ERROR;
    }
    
    my_printf(&huart2, "BNO080状态正常\r\n");
    return HAL_OK;
}
/**
 * @brief  配置BNO080旋转向量报告
 * @param  report_interval: 报告间隔(微秒)
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ConfigureRotationVector(uint32_t report_interval)
{
    uint8_t command[17] = {0};
    
    // 构建设置特征命令
    command[0] = BNO080_CMD_SET_FEATURE;  // 设置特征命令
    command[1] = BNO080_SENSOR_ROTATION_VECTOR;  // 旋转向量传感器
    
    // 设置报告间隔 (微秒，小端格式)
    command[2] = (uint8_t)(report_interval & 0xFF);
    command[3] = (uint8_t)((report_interval >> 8) & 0xFF);
    command[4] = (uint8_t)((report_interval >> 16) & 0xFF);
    command[5] = (uint8_t)((report_interval >> 24) & 0xFF);
    
    // 发送命令到命令寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            command, 
                                            sizeof(command), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "配置旋转向量报告失败\r\n");
        return status;
    }
    
    my_printf(&huart2, "成功配置旋转向量报告，间隔: %lu微秒\r\n", report_interval);
    return HAL_OK;
}

/**
 * @brief  配置BNO080游戏旋转向量报告
 * @param  report_interval: 报告间隔(微秒)
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ConfigureGameRotationVector(uint32_t report_interval)
{
    uint8_t command[17] = {0};
    
    // 构建设置特征命令
    command[0] = BNO080_CMD_SET_FEATURE;  // 设置特征命令
    command[1] = BNO080_SENSOR_GAME_ROTATION_VECTOR;  // 游戏旋转向量传感器
    
    // 设置报告间隔 (微秒，小端格式)
    command[2] = (uint8_t)(report_interval & 0xFF);
    command[3] = (uint8_t)((report_interval >> 8) & 0xFF);
    command[4] = (uint8_t)((report_interval >> 16) & 0xFF);
    command[5] = (uint8_t)((report_interval >> 24) & 0xFF);
    
    // 发送命令到命令寄存器
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            command, 
                                            sizeof(command), 
                                            100);
    
    if (status != HAL_OK)
    {
        my_printf(&huart2, "配置游戏旋转向量报告失败\r\n");
        return status;
    }
    
    my_printf(&huart2, "成功配置游戏旋转向量报告，间隔: %lu微秒\r\n", report_interval);
    return HAL_OK;
}

/**
 * @brief  读取BNO080传感器数据
 * @param  data_buffer: 数据缓冲区
 * @param  buffer_size: 缓冲区大小
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_ReadSensorData(uint8_t* data_buffer, uint16_t buffer_size)
{
    // 检查参数
    if (data_buffer == NULL || buffer_size < sizeof(BNO080_RotationVector_t))
    {
        return HAL_ERROR;
    }
    
    // 从输入报告寄存器读取数据
    HAL_StatusTypeDef status = I2C_Mem_Read(g_bno080_i2c_address << 1, 
                                           BNO080_REG_INPUT_REPORT, 
                                           1, 
                                           data_buffer, 
                                           buffer_size, 
                                           100);
    
    if (status != HAL_OK)
    {
        g_bno080_data.error_count++;
        my_printf(&huart2, "读取传感器数据失败, 错误码: %d\r\n", status);
        return status;
    }
    
    // 检查报告ID是否为旋转向量或游戏旋转向量
    if (data_buffer[0] != BNO080_SENSOR_ROTATION_VECTOR && 
        data_buffer[0] != BNO080_SENSOR_GAME_ROTATION_VECTOR)
    {
        // 不是我们期望的报告类型
        return HAL_ERROR;
    }
    
    return HAL_OK;
}

/**
 * @brief  启动非阻塞I2C数据读取
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_I2C_StartNonBlockingRead(void)
{
    // 检查当前状态
    if (g_bno080_i2c_state != BNO080_I2C_STATE_IDLE)
    {
        return HAL_BUSY;
    }
    
    // 更新状态
    g_bno080_i2c_state = BNO080_I2C_STATE_READING;
    
    // 启动非阻塞读取
    HAL_StatusTypeDef status = HAL_I2C_Mem_Read_IT(&hi2c1, 
                                                  g_bno080_i2c_address << 1, 
                                                  BNO080_REG_INPUT_REPORT, 
                                                  I2C_MEMADD_SIZE_8BIT, 
                                                  g_i2c_rx_buffer, 
                                                  sizeof(BNO080_RotationVector_t));
    
    if (status != HAL_OK)
    {
        g_bno080_i2c_state = BNO080_I2C_STATE_ERROR;
        g_bno080_data.error_count++;
        my_printf(&huart2, "启动非阻塞读取失败, 错误码: %d\r\n", status);
    }
    
    return status;
}

/**
 * @brief  处理I2C接收到的数据
 * @param  data: 数据指针
 * @param  length: 数据长度
 */
void BNO080_I2C_ProcessData(uint8_t* data, uint16_t length)
{
    // 检查参数
    if (data == NULL || length < sizeof(BNO080_RotationVector_t))
    {
        return;
    }
    
    // 检查报告ID
    if (data[0] != BNO080_SENSOR_ROTATION_VECTOR && 
        data[0] != BNO080_SENSOR_GAME_ROTATION_VECTOR)
    {
        // 不是我们期望的报告类型
        return;
    }
    
    // 将数据转换为旋转向量结构体
    BNO080_RotationVector_t* rv = (BNO080_RotationVector_t*)data;
    
    // 从Q14格式转换为浮点数 (优化：使用位移代替除法)
    const float q14_scale = 1.0f / 16384.0f;
    float qx = (float)rv->i_component * q14_scale;
    float qy = (float)rv->j_component * q14_scale;
    float qz = (float)rv->k_component * q14_scale;
    float qw = (float)rv->real_component * q14_scale;
    
    // 四元数归一化 (优化：使用快速平方根倒数)
    float norm_sq = qw*qw + qx*qx + qy*qy + qz*qz;
    if (norm_sq > 1e-6f)  // 避免除零
    {
        float inv_norm = 1.0f / sqrtf(norm_sq);
        qw *= inv_norm;
        qx *= inv_norm;
        qy *= inv_norm;
        qz *= inv_norm;
    }
    
    // 四元数转欧拉角 (优化：预计算常用项)
    const float rad_to_deg = 180.0f / 3.14159265358979323846f;
    
    // 预计算常用项
    float qx_qy = qx * qy;
    float qw_qz = qw * qz;
    float qx_qx = qx * qx;
    float qy_qy = qy * qy;
    float qz_qz = qz * qz;
    float qw_qx = qw * qx;
    float qy_qz = qy * qz;
    float qw_qy = qw * qy;
    float qz_qx = qz * qx;
    
    // 计算欧拉角
    float yaw = atan2f(2.0f * (qw_qz + qx_qy), 1.0f - 2.0f * (qy_qy + qz_qz)) * rad_to_deg;
    float pitch = asinf(2.0f * (qw_qy - qz_qx)) * rad_to_deg;
    float roll = atan2f(2.0f * (qw_qx + qy_qz), 1.0f - 2.0f * (qx_qx + qy_qy)) * rad_to_deg;
    
    // 更新全局数据结构
    __disable_irq();
    g_bno080_data.yaw = yaw;
    g_bno080_data.pitch = pitch;
    g_bno080_data.roll = roll;
    g_bno080_data.new_data_flag = 1;
    g_bno080_data.last_update_time = HAL_GetTick();
    __enable_irq();
}

/**
 * @brief  I2C主机接收完成回调函数
 * @param  hi2c: I2C句柄
 */
void HAL_I2C_MasterRxCpltCallback(I2C_HandleTypeDef *hi2c)
{
    if (hi2c->Instance == hi2c1.Instance)
    {
        if (g_bno080_i2c_state == BNO080_I2C_STATE_READING)
        {
            // 更新状态
            g_bno080_i2c_state = BNO080_I2C_STATE_PROCESSING;
            
            // 处理接收到的数据
            BNO080_I2C_ProcessData(g_i2c_rx_buffer, sizeof(BNO080_RotationVector_t));
            
            // 恢复空闲状态
            g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
        }
    }
}

/**
 * @brief  I2C错误回调函数
 * @param  hi2c: I2C句柄
 */
void HAL_I2C_ErrorCallback(I2C_HandleTypeDef *hi2c)
{
    if (hi2c->Instance == hi2c1.Instance)
    {
        // 记录错误
        g_bno080_data.error_count++;
        
        // 恢复空闲状态
        g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
        
        my_printf(&huart2, "I2C通信错误, 错误码: %d\r\n", hi2c->ErrorCode);
    }
}

/**
 * @brief  错误处理函数
 * @param  error: 错误类型
 */
void BNO080_ErrorHandler(BNO080_Error_t error)
{
    // 更新错误统计
    g_error_stats.total_error_count++;
    g_error_stats.last_error = error;
    g_error_stats.last_error_time = HAL_GetTick();
    
    // 根据错误类型进行分类统计
    switch (error)
    {
        case BNO080_ERROR_I2C_TIMEOUT:
            g_error_stats.i2c_timeout_count++;
            my_printf(&huart2, "BNO080错误: I2C超时\r\n");
            break;
            
        case BNO080_ERROR_I2C_NACK:
            g_error_stats.i2c_nack_count++;
            my_printf(&huart2, "BNO080错误: I2C NACK\r\n");
            break;
            
        case BNO080_ERROR_DEVICE_NOT_FOUND:
            g_error_stats.device_offline_count++;
            g_device_online = 0;
            my_printf(&huart2, "BNO080错误: 设备未找到\r\n");
            break;
            
        case BNO080_ERROR_INVALID_DATA:
            g_error_stats.invalid_data_count++;
            my_printf(&huart2, "BNO080错误: 无效数据\r\n");
            break;
            
        case BNO080_ERROR_CHECKSUM_FAIL:
            g_error_stats.invalid_data_count++;
            my_printf(&huart2, "BNO080错误: 校验和失败\r\n");
            break;
            
        case BNO080_ERROR_CONFIG_FAIL:
            my_printf(&huart2, "BNO080错误: 配置失败\r\n");
            break;
            
        default:
            my_printf(&huart2, "BNO080错误: 未知错误\r\n");
            break;
    }
    
    // 尝试恢复
    if (g_error_stats.total_error_count % 5 == 0)  // 每5次错误尝试一次恢复
    {
        BNO080_RecoveryAttempt();
    }
}

/**
 * @brief  恢复尝试函数
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_RecoveryAttempt(void)
{
    g_error_stats.recovery_count++;
    my_printf(&huart2, "BNO080开始恢复尝试 #%lu\r\n", g_error_stats.recovery_count);
    
    // 重置I2C状态机
    g_bno080_i2c_state = BNO080_I2C_STATE_IDLE;
    
    // 检查设备是否在线
    if (BNO080_I2C_IsReady() != HAL_OK)
    {
        BNO080_ErrorHandler(BNO080_ERROR_DEVICE_NOT_FOUND);
        return HAL_ERROR;
    }
    
    // 设备重新上线
    g_device_online = 1;
    
    // 发送复位命令
    if (BNO080_I2C_Reset() != HAL_OK)
    {
        BNO080_ErrorHandler(BNO080_ERROR_CONFIG_FAIL);
        return HAL_ERROR;
    }
    
    // 等待复位完成
    HAL_Delay(100);
    
    // 重新配置传感器
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) != HAL_OK)
    {
        // 尝试配置游戏旋转向量报告 (备选方案)
        if (BNO080_I2C_ConfigureGameRotationVector(BNO080_REPORT_INTERVAL_10MS) != HAL_OK)
        {
            BNO080_ErrorHandler(BNO080_ERROR_CONFIG_FAIL);
            return HAL_ERROR;
        }
    }
    
    my_printf(&huart2, "BNO080恢复成功\r\n");
    return HAL_OK;
}

/**
 * @brief  获取错误统计信息
 * @param  stats: 错误统计结构体指针
 */
void BNO080_GetErrorStats(BNO080_ErrorStats_t* stats)
{
    if (stats != NULL)
    {
        __disable_irq();
        memcpy(stats, &g_error_stats, sizeof(BNO080_ErrorStats_t));
        __enable_irq();
    }
}

/**
 * @brief  清除错误统计信息
 */
void BNO080_ClearErrorStats(void)
{
    __disable_irq();
    memset(&g_error_stats, 0, sizeof(BNO080_ErrorStats_t));
    __enable_irq();
    
    my_printf(&huart2, "BNO080错误统计已清除\r\n");
}

/**
 * @brief  检查设备是否在线
 * @retval 1: 在线, 0: 离线
 */
uint8_t BNO080_IsDeviceOnline(void)
{
    return g_device_online;
}

/**
**
 * @brief  进入低功耗模式
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_EnterLowPowerMode(void)
{
    // 停止定时器以节省功耗
    HAL_TIM_Base_Stop_IT(&htim2);
    
    // 发送低功耗命令到BNO080
    uint8_t low_power_cmd[4] = {0x02, 0x00, 0x01, 0x00}; // 进入睡眠模式
    
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            low_power_cmd, 
                                            sizeof(low_power_cmd), 
                                            100);
    
    if (status == HAL_OK)
    {
        my_printf(&huart2, "BNO080已进入低功耗模式\r\n");
    }
    else
    {
        my_printf(&huart2, "BNO080进入低功耗模式失败\r\n");
    }
    
    return status;
}

/**
 * @brief  退出低功耗模式
 * @retval HAL状态
 */
HAL_StatusTypeDef BNO080_ExitLowPowerMode(void)
{
    // 发送唤醒命令到BNO080
    uint8_t wake_cmd[4] = {0x02, 0x00, 0x00, 0x00}; // 退出睡眠模式
    
    HAL_StatusTypeDef status = I2C_Mem_Write(g_bno080_i2c_address << 1, 
                                            BNO080_REG_COMMAND, 
                                            1, 
                                            wake_cmd, 
                                            sizeof(wake_cmd), 
                                            100);
    
    if (status == HAL_OK)
    {
        // 等待设备唤醒
        HAL_Delay(50);
        
        // 重新启动定时器
        HAL_TIM_Base_Start_IT(&htim2);
        
        my_printf(&huart2, "BNO080已退出低功耗模式\r\n");
    }
    else
    {
        my_printf(&huart2, "BNO080退出低功耗模式失败\r\n");
    }
    
    return status;
}

/**
 * @brief  优化性能设置
 */
void BNO080_OptimizePerformance(void)
{
    // 优化I2C时钟频率 (已在初始化时设置为400kHz)
    
    // 优化中断优先级
    HAL_NVIC_SetPriority(TIM2_IRQn, 1, 0);  // 设置较高优先级
    HAL_NVIC_SetPriority(I2C1_EV_IRQn, 2, 0);  // I2C事件中断
    HAL_NVIC_SetPriority(I2C1_ER_IRQn, 2, 0);  // I2C错误中断
    
    // 启用I2C快速模式
    hi2c1.Init.ClockSpeed = 400000;  // 400kHz快速模式
    
    my_printf(&huart2, "BNO080性能优化已应用\r\n");
}

/**
**
 * @brief  设置调试级别
 * @param  level: 调试级别
 */
void BNO080_SetDebugLevel(BNO080_DebugLevel_t level)
{
    g_debug_level = level;
    
    switch (level)
    {
        case BNO080_DEBUG_OFF:
            my_printf(&huart2, "BNO080调试已关闭\r\n");
            break;
        case BNO080_DEBUG_BASIC:
            my_printf(&huart2, "BNO080调试级别: 基础\r\n");
            break;
        case BNO080_DEBUG_DETAILED:
            my_printf(&huart2, "BNO080调试级别: 详细\r\n");
            break;
        case BNO080_DEBUG_VERBOSE:
            my_printf(&huart2, "BNO080调试级别: 详尽\r\n");
            break;
    }
}

/**
 * @brief  获取性能统计信息
 * @param  stats: 性能统计结构体指针
 */
void BNO080_GetPerformanceStats(BNO080_PerformanceStats_t* stats)
{
    if (stats != NULL)
    {
        __disable_irq();
        memcpy(stats, &g_performance_stats, sizeof(BNO080_PerformanceStats_t));
        __enable_irq();
    }
}

/**
 * @brief  清除性能统计信息
 */
void BNO080_ClearPerformanceStats(void)
{
    __disable_irq();
    memset(&g_performance_stats, 0, sizeof(BNO080_PerformanceStats_t));
    __enable_irq();
    
    my_printf(&huart2, "BNO080性能统计已清除\r\n");
}

/**
 * @brief  打印诊断信息
 */
void BNO080_PrintDiagnostics(void)
{
    my_printf(&huart2, "\r\n=== BNO080诊断信息 ===\r\n");
    
    // 设备状态
    my_printf(&huart2, "设备状态: %s\r\n", g_device_online ? "在线" : "离线");
    my_printf(&huart2, "I2C地址: 0x%02X\r\n", g_bno080_i2c_address);
    my_printf(&huart2, "I2C状态: %d\r\n", g_bno080_i2c_state);
    
    // 数据状态
    my_printf(&huart2, "最后更新时间: %lu ms\r\n", g_bno080_data.last_update_time);
    my_printf(&huart2, "当前姿态: Yaw=%.2f°, Pitch=%.2f°, Roll=%.2f°\r\n", 
              g_bno080_data.yaw, g_bno080_data.pitch, g_bno080_data.roll);
    
    // 错误统计
    my_printf(&huart2, "总错误次数: %lu\r\n", g_error_stats.total_error_count);
    my_printf(&huart2, "I2C超时: %lu\r\n", g_error_stats.i2c_timeout_count);
    my_printf(&huart2, "I2C NACK: %lu\r\n", g_error_stats.i2c_nack_count);
    my_printf(&huart2, "设备离线: %lu\r\n", g_error_stats.device_offline_count);
    my_printf(&huart2, "无效数据: %lu\r\n", g_error_stats.invalid_data_count);
    my_printf(&huart2, "恢复尝试: %lu\r\n", g_error_stats.recovery_count);
    
    // 性能统计
    my_printf(&huart2, "总读取次数: %lu\r\n", g_performance_stats.total_reads);
    my_printf(&huart2, "成功读取: %lu\r\n", g_performance_stats.successful_reads);
    my_printf(&huart2, "失败读取: %lu\r\n", g_performance_stats.failed_reads);
    my_printf(&huart2, "平均读取时间: %lu μs\r\n", g_performance_stats.average_read_time_us);
    my_printf(&huart2, "数据更新率: %lu Hz\r\n", g_performance_stats.data_update_rate_hz);
    
    my_printf(&huart2, "=== 诊断信息结束 ===\r\n\r\n");
}

/**
 * @brief  测试模式
 */
void BNO080_TestMode(void)
{
    my_printf(&huart2, "\r\n=== BNO080测试模式 ===\r\n");
    
    // 测试I2C通信
    my_printf(&huart2, "测试I2C通信...\r\n");
    if (BNO080_I2C_IsReady() == HAL_OK)
    {
        my_printf(&huart2, "✓ I2C通信正常\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ I2C通信失败\r\n");
    }
    
    // 测试设备复位
    my_printf(&huart2, "测试设备复位...\r\n");
    if (BNO080_I2C_Reset() == HAL_OK)
    {
        my_printf(&huart2, "✓ 设备复位成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 设备复位失败\r\n");
    }
    
    // 等待复位完成
    HAL_Delay(200);
    
    // 测试状态检查
    my_printf(&huart2, "测试状态检查...\r\n");
    if (BNO080_I2C_CheckStatus() == HAL_OK)
    {
        my_printf(&huart2, "✓ 设备状态正常\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 设备状态异常\r\n");
    }
    
    // 测试产品ID读取
    my_printf(&huart2, "测试产品ID读取...\r\n");
    uint8_t product_id[20] = {0};
    uint16_t id_length = 0;
    if (BNO080_I2C_ReadProductID(product_id, &id_length) == HAL_OK)
    {
        my_printf(&huart2, "✓ 产品ID读取成功: ");
        for (uint16_t i = 0; i < id_length; i++)
        {
            my_printf(&huart2, "%02X ", product_id[i]);
        }
        my_printf(&huart2, "\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 产品ID读取失败\r\n");
    }
    
    // 测试传感器配置
    my_printf(&huart2, "测试传感器配置...\r\n");
    if (BNO080_I2C_ConfigureRotationVector(BNO080_REPORT_INTERVAL_10MS) == HAL_OK)
    {
        my_printf(&huart2, "✓ 旋转向量配置成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 旋转向量配置失败\r\n");
    }
    
    // 测试数据读取
    my_printf(&huart2, "测试数据读取...\r\n");
    uint8_t test_buffer[32] = {0};
    if (BNO080_I2C_ReadSensorData(test_buffer, sizeof(test_buffer)) == HAL_OK)
    {
        my_printf(&huart2, "✓ 数据读取成功\r\n");
    }
    else
    {
        my_printf(&huart2, "✗ 数据读取失败\r\n");
    }
    
    my_printf(&huart2, "=== 测试模式结束 ===\r\n\r\n");
}