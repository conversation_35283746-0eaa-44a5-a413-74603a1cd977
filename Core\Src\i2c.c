/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file    i2c.c
  * @brief   This file provides code for the configuration
  *          of the I2C instances.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "i2c.h"

/* USER CODE BEGIN 0 */
#include <string.h>
#include <stdio.h>
#include "usart.h"  // 用于调试输出
/* USER CODE END 0 */

I2C_HandleTypeDef hi2c1;

/* I2C1 init function */
void MX_I2C1_Init(void)
{

  /* USER CODE BEGIN I2C1_Init 0 */

  /* USER CODE END I2C1_Init 0 */

  /* USER CODE BEGIN I2C1_Init 1 */

  /* USER CODE END I2C1_Init 1 */
  hi2c1.Instance = I2C1;
  hi2c1.Init.ClockSpeed = 400000;
  hi2c1.Init.DutyCycle = I2C_DUTYCYCLE_2;
  hi2c1.Init.OwnAddress1 = 0;
  hi2c1.Init.AddressingMode = I2C_ADDRESSINGMODE_7BIT;
  hi2c1.Init.DualAddressMode = I2C_DUALADDRESS_DISABLE;
  hi2c1.Init.OwnAddress2 = 0;
  hi2c1.Init.GeneralCallMode = I2C_GENERALCALL_DISABLE;
  hi2c1.Init.NoStretchMode = I2C_NOSTRETCH_DISABLE;
  if (HAL_I2C_Init(&hi2c1) != HAL_OK)
  {
    Error_Handler();
  }
  /* USER CODE BEGIN I2C1_Init 2 */

  /* USER CODE END I2C1_Init 2 */

}

void HAL_I2C_MspInit(I2C_HandleTypeDef* i2cHandle)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};
  if(i2cHandle->Instance==I2C1)
  {
  /* USER CODE BEGIN I2C1_MspInit 0 */

  /* USER CODE END I2C1_MspInit 0 */

    __HAL_RCC_GPIOB_CLK_ENABLE();
    /**I2C1 GPIO Configuration
    PB6     ------> I2C1_SCL
    PB7     ------> I2C1_SDA
    */
    GPIO_InitStruct.Pin = GPIO_PIN_6|GPIO_PIN_7;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_OD;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);

    /* I2C1 clock enable */
    __HAL_RCC_I2C1_CLK_ENABLE();

    /* I2C1 interrupt Init */
    HAL_NVIC_SetPriority(I2C1_EV_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(I2C1_EV_IRQn);
    HAL_NVIC_SetPriority(I2C1_ER_IRQn, 2, 0);
    HAL_NVIC_EnableIRQ(I2C1_ER_IRQn);
  /* USER CODE BEGIN I2C1_MspInit 1 */

  /* USER CODE END I2C1_MspInit 1 */
  }
}

void HAL_I2C_MspDeInit(I2C_HandleTypeDef* i2cHandle)
{

  if(i2cHandle->Instance==I2C1)
  {
  /* USER CODE BEGIN I2C1_MspDeInit 0 */

  /* USER CODE END I2C1_MspDeInit 0 */
    /* Peripheral clock disable */
    __HAL_RCC_I2C1_CLK_DISABLE();

    /**I2C1 GPIO Configuration
    PB6     ------> I2C1_SCL
    PB7     ------> I2C1_SDA
    */
    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_6);

    HAL_GPIO_DeInit(GPIOB, GPIO_PIN_7);

    /* I2C1 interrupt Deinit */
    HAL_NVIC_DisableIRQ(I2C1_EV_IRQn);
    HAL_NVIC_DisableIRQ(I2C1_ER_IRQn);
  /* USER CODE BEGIN I2C1_MspDeInit 1 */

  /* USER CODE END I2C1_MspDeInit 1 */
  }
}

/* USER CODE BEGIN 1 */

/**
  * @brief  检查I2C设备是否就绪
  * @param  DevAddress: 设备地址
  * @param  Trials: 尝试次数
  * @retval HAL状态
  */
HAL_StatusTypeDef I2C_IsDeviceReady(uint8_t DevAddress, uint32_t Trials)
{
  HAL_StatusTypeDef status = HAL_I2C_IsDeviceReady(&hi2c1, DevAddress, Trials, 1000);
  
  if (status != HAL_OK)
  {
    my_printf(&huart2, "I2C设备 0x%02X 未就绪, 错误码: %d\r\n", DevAddress, status);
  }
  else
  {
    my_printf(&huart2, "I2C设备 0x%02X 就绪\r\n", DevAddress);
  }
  
  return status;
}

/**
  * @brief  I2C主机发送数据
  * @param  DevAddress: 设备地址
  * @param  pData: 数据指针
  * @param  Size: 数据大小
  * @param  Timeout: 超时时间
  * @retval HAL状态
  */
HAL_StatusTypeDef I2C_Master_Transmit(uint16_t DevAddress, uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
  HAL_StatusTypeDef status = HAL_I2C_Master_Transmit(&hi2c1, DevAddress, pData, Size, Timeout);
  
  if (status != HAL_OK)
  {
    my_printf(&huart2, "I2C发送失败, 错误码: %d\r\n", status);
  }
  
  return status;
}

/**
  * @brief  I2C主机接收数据
  * @param  DevAddress: 设备地址
  * @param  pData: 数据指针
  * @param  Size: 数据大小
  * @param  Timeout: 超时时间
  * @retval HAL状态
  */
HAL_StatusTypeDef I2C_Master_Receive(uint16_t DevAddress, uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
  HAL_StatusTypeDef status = HAL_I2C_Master_Receive(&hi2c1, DevAddress, pData, Size, Timeout);
  
  if (status != HAL_OK)
  {
    my_printf(&huart2, "I2C接收失败, 错误码: %d\r\n", status);
  }
  
  return status;
}

/**
  * @brief  I2C写入内存
  * @param  DevAddress: 设备地址
  * @param  MemAddress: 内存地址
  * @param  MemAddSize: 内存地址大小
  * @param  pData: 数据指针
  * @param  Size: 数据大小
  * @param  Timeout: 超时时间
  * @retval HAL状态
  */
HAL_StatusTypeDef I2C_Mem_Write(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
  HAL_StatusTypeDef status = HAL_I2C_Mem_Write(&hi2c1, DevAddress, MemAddress, MemAddSize, pData, Size, Timeout);
  
  if (status != HAL_OK)
  {
    my_printf(&huart2, "I2C内存写入失败, 错误码: %d\r\n", status);
  }
  
  return status;
}

/**
  * @brief  I2C读取内存
  * @param  DevAddress: 设备地址
  * @param  MemAddress: 内存地址
  * @param  MemAddSize: 内存地址大小
  * @param  pData: 数据指针
  * @param  Size: 数据大小
  * @param  Timeout: 超时时间
  * @retval HAL状态
  */
HAL_StatusTypeDef I2C_Mem_Read(uint16_t DevAddress, uint16_t MemAddress, uint16_t MemAddSize, uint8_t *pData, uint16_t Size, uint32_t Timeout)
{
  HAL_StatusTypeDef status = HAL_I2C_Mem_Read(&hi2c1, DevAddress, MemAddress, MemAddSize, pData, Size, Timeout);
  
  if (status != HAL_OK)
  {
    my_printf(&huart2, "I2C内存读取失败, 错误码: %d\r\n", status);
  }
  
  return status;
}

/* USER CODE END 1 */
