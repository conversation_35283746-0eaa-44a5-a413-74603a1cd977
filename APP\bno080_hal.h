/**
 * @file    bno080_hal.h
 * @brief   BNO080九轴传感器HAL驱动头文件
 */
#ifndef __BNO080_HAL_H__
#define __BNO080_HAL_H__

#include "stm32f1xx_hal.h"
#include <stdint.h>

// BNO080接收缓冲区大小
#define BNO080_RX_BUFFER_SIZE  128 

// RVC数据包相关定义
#define RVC_PACKET_SIZE 19
#define RVC_HEADER      0xAAAA

// BNO080 I2C地址 (根据ADR引脚连接决定)
#define BNO080_I2C_ADDR_ADR_LOW   0x4A  // ADR引脚接GND
#define BNO080_I2C_ADDR_ADR_HIGH  0x4B  // ADR引脚接VCC

// BNO080 HID over I2C寄存器地址
#define BNO080_REG_HID_DESC       0x01  // HID描述符寄存器
#define BNO080_REG_REPORT_DESC    0x02  // 报告描述符寄存器
#define BNO080_REG_INPUT_REPORT   0x03  // 输入报告寄存器
#define BNO080_REG_OUTPUT_REPORT  0x04  // 输出报告寄存器
#define BNO080_REG_COMMAND        0x05  // 命令寄存器
#define BNO080_REG_DATA           0x06  // 数据寄存器

// BNO080 HID命令
#define BNO080_CMD_RESET          0x01  // 复位命令
#define BNO080_CMD_GET_FEATURE    0x02  // 获取特征命令
#define BNO080_CMD_SET_FEATURE    0x03  // 设置特征命令
#define BNO080_CMD_GET_FRS        0x04  // 获取FRS命令

// BNO080 通道ID
#define BNO080_CHANNEL_COMMAND    0x00  // 命令通道
#define BNO080_CHANNEL_EXECUTABLE 0x01  // 可执行通道
#define BNO080_CHANNEL_CONTROL    0x02  // 控制通道
#define BNO080_CHANNEL_REPORTS    0x03  // 报告通道
#define BNO080_CHANNEL_WAKE       0x04  // 唤醒通道
#define BNO080_CHANNEL_GYRO       0x05  // 陀螺仪通道

// BNO080 报告ID
#define BNO080_REPORT_PRODUCT_ID  0xF8  // 产品ID报告
#define BNO080_REPORT_ROTATION_VECTOR 0x05  // 旋转向量报告
#define BNO080_REPORT_GAME_ROTATION_VECTOR 0x08  // 游戏旋转向量报告

// BNO080 特征报告参考
#define BNO080_FRS_READ_PRODUCT_ID 0xF8  // 读取产品ID
#define BNO080_FRS_READ_SENSOR_ID  0xF9  // 读取传感器ID

// BNO080 传感器报告类型
#define BNO080_SENSOR_ROTATION_VECTOR           0x05  // 旋转向量
#define BNO080_SENSOR_GAME_ROTATION_VECTOR      0x08  // 游戏旋转向量
#define BNO080_SENSOR_GEOMAGNETIC_ROTATION_VECTOR 0x09  // 地磁旋转向量
#define BNO080_SENSOR_ACCELEROMETER             0x01  // 加速度计
#define BNO080_SENSOR_GYROSCOPE                 0x02  // 陀螺仪
#define BNO080_SENSOR_MAGNETIC_FIELD            0x03  // 磁力计

// BNO080 报告间隔 (微秒)
#define BNO080_REPORT_INTERVAL_10MS   10000   // 100Hz
#define BNO080_REPORT_INTERVAL_20MS   20000   // 50Hz
#define BNO080_REPORT_INTERVAL_50MS   50000   // 20Hz
#define BNO080_REPORT_INTERVAL_100MS  100000  // 10Hz

// BNO080 I2C状态机状态
typedef enum {
    BNO080_I2C_STATE_IDLE,
    BNO080_I2C_STATE_READING,
    BNO080_I2C_STATE_PROCESSING,
    BNO080_I2C_STATE_ERROR
} BNO080_I2C_State_t;

// BNO080错误类型
typedef enum {
    BNO080_ERROR_NONE = 0,
    BNO080_ERROR_I2C_TIMEOUT,
    BNO080_ERROR_I2C_NACK,
    BNO080_ERROR_DEVICE_NOT_FOUND,
    BNO080_ERROR_INVALID_DATA,
    BNO080_ERROR_CHECKSUM_FAIL,
    BNO080_ERROR_CONFIG_FAIL
} BNO080_Error_t;

// BNO080错误统计结构体
typedef struct {
    uint32_t i2c_timeout_count;
    uint32_t i2c_nack_count;
    uint32_t device_offline_count;
    uint32_t invalid_data_count;
    uint32_t total_error_count;
    uint32_t retry_count;
    uint32_t recovery_count;
    BNO080_Error_t last_error;
    uint32_t last_error_time;
} BNO080_ErrorStats_t;

// BNO080性能统计结构体
typedef struct {
    uint32_t total_reads;
    uint32_t successful_reads;
    uint32_t failed_reads;
    uint32_t average_read_time_us;
    uint32_t max_read_time_us;
    uint32_t min_read_time_us;
    uint32_t data_update_rate_hz;
    uint32_t last_update_time;
} BNO080_PerformanceStats_t;

// 调试模式定义
typedef enum {
    BNO080_DEBUG_OFF = 0,
    BNO080_DEBUG_BASIC,
    BNO080_DEBUG_DETAILED,
    BNO080_DEBUG_VERBOSE
} BNO080_DebugLevel_t;

// BNO080旋转向量数据包结构
typedef struct __packed {
    uint8_t report_id;          // 报告ID (0x05)
    uint8_t sequence_number;    // 序列号
    uint8_t status;            // 状态字节
    uint16_t delay;            // 延迟时间
    int16_t i_component;       // 四元数i分量 (Q14)
    int16_t j_component;       // 四元数j分量 (Q14)  
    int16_t k_component;       // 四元数k分量 (Q14)
    int16_t real_component;    // 四元数实部 (Q14)
    uint16_t accuracy;         // 精度估计
} BNO080_RotationVector_t;

// BNO080数据结构
typedef struct {
    float yaw;      // 偏航角 (度)
    float pitch;    // 俯仰角 (度)
    float roll;     // 横滚角 (度)
    volatile uint8_t new_data_flag;  // 新数据标志
    uint32_t last_update_time;  // 最后更新时间
    uint32_t error_count;       // 错误计数
} BNO080_Data_t;

// 函数声明
void BNO080_Init(void);
void BNO080_GetData(BNO080_Data_t* data_ptr);
HAL_StatusTypeDef BNO080_I2C_IsReady(void);
void BNO080_ProcessReceivedData(uint16_t data_length);

// I2C通信相关函数
HAL_StatusTypeDef BNO080_I2C_Reset(void);
HAL_StatusTypeDef BNO080_I2C_ReadProductID(uint8_t* product_id, uint16_t* length);
HAL_StatusTypeDef BNO080_I2C_CheckStatus(void);
HAL_StatusTypeDef BNO080_I2C_ConfigureRotationVector(uint32_t report_interval);
HAL_StatusTypeDef BNO080_I2C_ConfigureGameRotationVector(uint32_t report_interval);
HAL_StatusTypeDef BNO080_I2C_ReadSensorData(uint8_t* data_buffer, uint16_t buffer_size);
HAL_StatusTypeDef BNO080_I2C_StartNonBlockingRead(void);
void BNO080_I2C_ProcessData(uint8_t* data, uint16_t length);
void HAL_I2C_MasterRxCpltCallback(I2C_HandleTypeDef *hi2c);

// 错误处理和恢复相关函数
void BNO080_ErrorHandler(BNO080_Error_t error);
HAL_StatusTypeDef BNO080_RecoveryAttempt(void);
void BNO080_GetErrorStats(BNO080_ErrorStats_t* stats);
void BNO080_ClearErrorStats(void);
uint8_t BNO080_IsDeviceOnline(void);

// 性能优化和低功耗相关函数
HAL_StatusTypeDef BNO080_EnterLowPowerMode(void);
HAL_StatusTypeDef BNO080_ExitLowPowerMode(void);
void BNO080_OptimizePerformance(void);

// 调试和诊断相关函数
void BNO080_SetDebugLevel(BNO080_DebugLevel_t level);
void BNO080_GetPerformanceStats(BNO080_PerformanceStats_t* stats);
void BNO080_ClearPerformanceStats(void);
void BNO080_PrintDiagnostics(void);
void BNO080_TestMode(void);

// 单元测试相关函数 (包含在bno080_test.h中)
// #include "bno080_test.h" 可以访问完整的测试功能

void BNO080_GPIO_Config(void);



// 外部变量声明
extern I2C_HandleTypeDef hi2c1;  // I2C1句柄
extern UART_HandleTypeDef huart2; // 用于调试输出

#endif /* __BNO080_HAL_H__ */
