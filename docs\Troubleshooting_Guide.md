# BNO080故障排除和调试指南

## 概述

本文档提供了BNO080 I2C传感器项目的详细故障排除步骤和调试方法，帮助开发者快速定位和解决常见问题。

## 故障排除流程图

```
开始
  ↓
检查硬件连接
  ↓
运行基础测试
  ↓
分析错误类型
  ↓
应用对应解决方案
  ↓
验证修复结果
  ↓
结束
```

## 常见问题分类

### 1. 硬件连接问题

#### 问题现象
- 设备检测失败
- I2C通信超时
- 数据全为0或异常值

#### 检查清单
- [ ] 电源连接（3.3V和GND）
- [ ] I2C引脚连接（PB6-SCL, PB7-SDA）
- [ ] 协议选择引脚（PB0-PS0, PB1-PS1）
- [ ] 上拉电阻（4.7kΩ）
- [ ] 线路长度和质量

#### 调试步骤

1. **电源检查**
   ```c
   // 使用万用表测量BNO080的VCC引脚
   // 应该显示3.3V ± 0.1V
   ```

2. **引脚连接验证**
   ```c
   // 在BNO080_GPIO_Config()函数中添加调试代码
   void BNO080_GPIO_Config(void)
   {
       HAL_GPIO_WritePin(GPIOB, GPIO_PIN_0, GPIO_PIN_RESET);  // PS0=0
       HAL_GPIO_WritePin(GPIOB, GPIO_PIN_1, GPIO_PIN_RESET);  // PS1=0
       
       // 验证引脚状态
       GPIO_PinState ps0_state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
       GPIO_PinState ps1_state = HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_1);
       
       my_printf(&huart2, "PS0状态: %d, PS1状态: %d\r\n", ps0_state, ps1_state);
   }
   ```

3. **I2C总线检查**
   ```c
   // 使用示波器或逻辑分析仪检查I2C信号
   // 或使用以下代码进行基础检查
   HAL_StatusTypeDef status = HAL_I2C_IsDeviceReady(&hi2c1, 0x4A << 1, 3, 1000);
   my_printf(&huart2, "I2C设备检测结果: %d\r\n", status);
   ```

### 2. I2C通信问题

#### 问题现象
- HAL_TIMEOUT错误
- HAL_ERROR返回值
- 频繁的NACK错误

#### 诊断代码
```c
void BNO080_DiagnoseI2C(void)
{
    my_printf(&huart2, "=== I2C诊断开始 ===\r\n");
    
    // 1. 检查I2C配置
    my_printf(&huart2, "I2C时钟频率: %lu Hz\r\n", hi2c1.Init.ClockSpeed);
    my_printf(&huart2, "I2C占空比: %d\r\n", hi2c1.Init.DutyCycle);
    
    // 2. 扫描I2C总线
    my_printf(&huart2, "扫描I2C总线...\r\n");
    for (uint8_t addr = 0x08; addr < 0x78; addr++) {
        if (HAL_I2C_IsDeviceReady(&hi2c1, addr << 1, 1, 10) == HAL_OK) {
            my_printf(&huart2, "发现设备: 0x%02X\r\n", addr);
        }
    }
    
    // 3. 测试BNO080地址
    uint8_t bno080_addresses[] = {0x4A, 0x4B};
    for (int i = 0; i < 2; i++) {
        HAL_StatusTypeDef status = HAL_I2C_IsDeviceReady(&hi2c1, 
                                                        bno080_addresses[i] << 1, 
                                                        3, 1000);
        my_printf(&huart2, "BNO080地址0x%02X: %s\r\n", 
                 bno080_addresses[i], 
                 (status == HAL_OK) ? "响应" : "无响应");
    }
    
    my_printf(&huart2, "=== I2C诊断结束 ===\r\n");
}
```

#### 解决方案

1. **降低I2C时钟频率**
   ```c
   // 从400kHz降低到100kHz
   hi2c1.Init.ClockSpeed = 100000;
   HAL_I2C_Init(&hi2c1);
   ```

2. **增加重试机制**
   ```c
   HAL_StatusTypeDef BNO080_I2C_ReadWithRetry(uint8_t* data, uint16_t size)
   {
       for (int retry = 0; retry < 3; retry++) {
           HAL_StatusTypeDef status = I2C_Mem_Read(g_bno080_i2c_address << 1,
                                                  BNO080_REG_INPUT_REPORT,
                                                  1, data, size, 100);
           if (status == HAL_OK) {
               return HAL_OK;
           }
           HAL_Delay(10);  // 重试间隔
       }
       return HAL_ERROR;
   }
   ```

3. **检查上拉电阻**
   - 确保SCL和SDA都有4.7kΩ上拉电阻
   - 如果线路较长，可以尝试2.2kΩ电阻

### 3. 数据异常问题

#### 问题现象
- 角度数据为NaN
- 数据超出合理范围
- 数据不更新

#### 调试代码
```c
void BNO080_DebugDataProcessing(uint8_t* raw_data, uint16_t length)
{
    my_printf(&huart2, "=== 数据处理调试 ===\r\n");
    
    // 1. 打印原始数据
    my_printf(&huart2, "原始数据 (%d字节): ", length);
    for (int i = 0; i < length; i++) {
        my_printf(&huart2, "%02X ", raw_data[i]);
    }
    my_printf(&huart2, "\r\n");
    
    // 2. 检查报告ID
    if (length > 0) {
        my_printf(&huart2, "报告ID: 0x%02X\r\n", raw_data[0]);
        
        if (raw_data[0] == 0x05) {  // 旋转向量报告
            BNO080_RotationVector_t* rv = (BNO080_RotationVector_t*)raw_data;
            
            // 3. 打印四元数原始值
            my_printf(&huart2, "四元数原始值: i=%d, j=%d, k=%d, real=%d\r\n",
                     rv->i_component, rv->j_component, 
                     rv->k_component, rv->real_component);
            
            // 4. 转换为浮点数
            float qx = (float)rv->i_component / 16384.0f;
            float qy = (float)rv->j_component / 16384.0f;
            float qz = (float)rv->k_component / 16384.0f;
            float qw = (float)rv->real_component / 16384.0f;
            
            my_printf(&huart2, "四元数浮点值: qw=%.4f, qx=%.4f, qy=%.4f, qz=%.4f\r\n",
                     qw, qx, qy, qz);
            
            // 5. 检查四元数模长
            float norm = sqrtf(qw*qw + qx*qx + qy*qy + qz*qz);
            my_printf(&huart2, "四元数模长: %.4f\r\n", norm);
            
            if (norm < 0.1f || norm > 2.0f) {
                my_printf(&huart2, "警告: 四元数模长异常!\r\n");
            }
        }
    }
    
    my_printf(&huart2, "=== 数据处理调试结束 ===\r\n");
}
```

#### 解决方案

1. **验证数据包完整性**
   ```c
   if (length < sizeof(BNO080_RotationVector_t)) {
       my_printf(&huart2, "错误: 数据包长度不足\r\n");
       return;
   }
   ```

2. **添加数据有效性检查**
   ```c
   bool BNO080_IsDataValid(float yaw, float pitch, float roll)
   {
       // 检查是否为NaN
       if (isnan(yaw) || isnan(pitch) || isnan(roll)) {
           return false;
       }
       
       // 检查范围
       if (yaw < -180.0f || yaw > 180.0f ||
           pitch < -90.0f || pitch > 90.0f ||
           roll < -180.0f || roll > 180.0f) {
           return false;
       }
       
       return true;
   }
   ```

3. **实现数据滤波**
   ```c
   typedef struct {
       float yaw_prev;
       float pitch_prev;
       float roll_prev;
       uint8_t initialized;
   } BNO080_Filter_t;
   
   void BNO080_ApplyFilter(BNO080_Data_t* data, BNO080_Filter_t* filter)
   {
       const float alpha = 0.1f;  // 滤波系数
       
       if (!filter->initialized) {
           filter->yaw_prev = data->yaw;
           filter->pitch_prev = data->pitch;
           filter->roll_prev = data->roll;
           filter->initialized = 1;
           return;
       }
       
       // 低通滤波
       data->yaw = alpha * data->yaw + (1.0f - alpha) * filter->yaw_prev;
       data->pitch = alpha * data->pitch + (1.0f - alpha) * filter->pitch_prev;
       data->roll = alpha * data->roll + (1.0f - alpha) * filter->roll_prev;
       
       // 更新历史值
       filter->yaw_prev = data->yaw;
       filter->pitch_prev = data->pitch;
       filter->roll_prev = data->roll;
   }
   ```

### 4. 性能问题

#### 问题现象
- 数据更新频率低于预期
- CPU占用率过高
- 系统响应缓慢

#### 性能监控代码
```c
void BNO080_MonitorPerformance(void)
{
    static uint32_t last_monitor_time = 0;
    static uint32_t data_count = 0;
    static uint32_t error_count = 0;
    
    uint32_t current_time = HAL_GetTick();
    
    // 每秒统计一次
    if (current_time - last_monitor_time >= 1000) {
        float data_rate = (float)data_count / 1.0f;  // Hz
        float error_rate = (float)error_count / (float)data_count * 100.0f;  // %
        
        my_printf(&huart2, "性能统计: 数据率=%.1fHz, 错误率=%.1f%%\r\n", 
                 data_rate, error_rate);
        
        // 重置计数器
        data_count = 0;
        error_count = 0;
        last_monitor_time = current_time;
    }
    
    data_count++;
}
```

#### 优化建议

1. **使用非阻塞I2C**
   ```c
   // 替换阻塞式调用
   // HAL_I2C_Mem_Read(&hi2c1, addr, reg, 1, data, size, timeout);
   
   // 使用非阻塞式调用
   HAL_I2C_Mem_Read_IT(&hi2c1, addr, reg, 1, data, size);
   ```

2. **优化中断优先级**
   ```c
   void BNO080_OptimizeInterrupts(void)
   {
       // I2C中断优先级
       HAL_NVIC_SetPriority(I2C1_EV_IRQn, 1, 0);
       HAL_NVIC_SetPriority(I2C1_ER_IRQn, 1, 0);
       
       // 定时器中断优先级
       HAL_NVIC_SetPriority(TIM2_IRQn, 2, 0);
       
       // 串口中断优先级（调试用，优先级较低）
       HAL_NVIC_SetPriority(USART2_IRQn, 3, 0);
   }
   ```

## 调试工具和方法

### 1. 内置调试功能

```c
// 设置调试级别
BNO080_SetDebugLevel(BNO080_DEBUG_VERBOSE);

// 打印完整诊断信息
BNO080_PrintDiagnostics();

// 运行测试模式
BNO080_TestMode();

// 获取错误统计
BNO080_ErrorStats_t stats;
BNO080_GetErrorStats(&stats);
```

### 2. 外部调试工具

#### 示波器检查
- **SCL信号**：应该是规整的时钟信号，频率400kHz
- **SDA信号**：应该有明显的数据变化
- **启动和停止条件**：检查I2C协议的START和STOP条件

#### 逻辑分析仪
- 捕获完整的I2C通信过程
- 分析协议层面的问题
- 验证地址、数据和ACK/NACK

### 3. 软件调试技巧

#### 断点调试
```c
// 在关键函数设置断点
void BNO080_I2C_ProcessData(uint8_t* data, uint16_t length)
{
    // 设置断点，检查data内容
    __NOP();  // 断点位置
    
    // 检查数据有效性
    if (data[0] != BNO080_SENSOR_ROTATION_VECTOR) {
        __NOP();  // 数据异常断点
    }
}
```

#### 内存监控
```c
void BNO080_CheckMemoryUsage(void)
{
    // 检查栈使用情况
    extern uint32_t _estack;
    extern uint32_t _Min_Stack_Size;
    
    uint32_t stack_start = (uint32_t)&_estack;
    uint32_t stack_size = (uint32_t)&_Min_Stack_Size;
    uint32_t current_sp;
    
    __asm volatile ("mov %0, sp" : "=r" (current_sp));
    
    uint32_t stack_used = stack_start - current_sp;
    float stack_usage = (float)stack_used / stack_size * 100.0f;
    
    my_printf(&huart2, "栈使用率: %.1f%% (%lu/%lu)\r\n", 
             stack_usage, stack_used, stack_size);
}
```

## 预防措施

### 1. 硬件设计建议
- 使用质量好的连接线
- 添加去耦电容（0.1μF和10μF）
- 保持I2C线路尽可能短
- 避免与高频信号线路并行

### 2. 软件设计建议
- 实现完善的错误处理
- 添加看门狗保护
- 使用状态机管理通信流程
- 定期进行自检

### 3. 测试建议
- 进行长时间稳定性测试
- 测试各种环境条件
- 验证错误恢复机制
- 进行电磁兼容性测试

## 常见错误代码对照表

| 错误代码 | HAL状态 | 可能原因 | 解决方案 |
|----------|---------|----------|----------|
| 0x01 | HAL_ERROR | 通用错误 | 检查硬件连接 |
| 0x02 | HAL_BUSY | I2C忙碌 | 等待或重置I2C |
| 0x03 | HAL_TIMEOUT | 通信超时 | 检查时钟和连接 |
| 0x04 | HAL_NACK | 设备无应答 | 检查地址和设备状态 |

## 联系支持

如果按照本指南仍无法解决问题，请提供以下信息：
1. 硬件连接图
2. 完整的错误日志
3. 示波器波形（如有）
4. 测试结果输出

联系方式：[技术支持邮箱]