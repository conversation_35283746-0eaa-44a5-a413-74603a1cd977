// This is an empty file to avoid duplicate compilation of system_stm32f1xx.c
// The actual implementation is in ../Core/Src/system_stm32f1xx.c

// Define empty symbols to satisfy the linker
// These will be overridden by the actual implementation
__attribute__((weak)) void SystemInit(void) {}
__attribute__((weak)) void SystemCoreClockUpdate(void) {}
__attribute__((weak)) uint32_t SystemCoreClock = 8000000;
__attribute__((weak)) const uint8_t AHBPrescTable[16] = {0};
__attribute__((weak)) const uint8_t APBPrescTable[8] = {0};